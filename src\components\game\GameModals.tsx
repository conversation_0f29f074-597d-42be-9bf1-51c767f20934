"use client";

import { useEffect, useState } from "react";

import { GameEgg } from "@/types/game";

interface GameModalsProps {
    selectedEgg: GameEgg | null;
    onEggOpen: (tokens: number) => void;
    onClose: () => void;
}

export function GameModals({ selectedEgg, onEggOpen, onClose }: GameModalsProps) {
    const [modalType, setModalType] = useState<"egg" | "chest" | "reward" | null>(null);
    const [rewardAmount, setRewardAmount] = useState(0);

    useEffect(() => {
        if (selectedEgg) {
            // 30% chance for chest, 70% for regular egg
            const isChest = Math.random() < 0.3;
            setModalType(isChest ? "chest" : "egg");
        } else {
            setModalType(null);
        }
    }, [selectedEgg]);

    const handleEggOpen = () => {
        if (!selectedEgg) return;

        const tokens = selectedEgg.eggType.tokenValue;
        setRewardAmount(tokens);
        setModalType("reward");
        onEggOpen(tokens);
    };

    const handleChestOpen = () => {
        // Random reward between 50-200 tokens for chest
        const tokens = Math.floor(Math.random() * 151) + 50;
        setRewardAmount(tokens);
        setModalType("reward");
        onEggOpen(tokens);
    };

    const handleClose = () => {
        setModalType(null);
        onClose();
    };

    if (!selectedEgg || !modalType) return null;

    return (
        <>
            {/* Egg Collection Modal */}
            {modalType === "egg" && (
                <div className="fixed inset-0 flex items-center justify-center z-50 animate-fadeIn">
                    <div className="bg-black bg-opacity-50 absolute inset-0" onClick={handleClose}></div>
                    <div className="bg-white rounded-2xl p-6 max-w-xs w-full mx-4 z-30 text-center animate-scaleIn">
                        <div className="w-24 h-24 mx-auto mb-4 text-6xl flex items-center justify-center">
                            {selectedEgg.eggType.type === "gold"
                                ? "🏆"
                                : selectedEgg.eggType.type === "silver"
                                  ? "💰"
                                  : "🎁"}
                        </div>
                        <h3 className="font-bold text-xl mb-2">You found a {selectedEgg.eggType.type} treasure!</h3>
                        <p className="text-gray-700 mb-4">Open it to reveal your reward!</p>
                        <button
                            onClick={handleEggOpen}
                            className="bg-gradient-to-r from-green-400 to-teal-500 text-white font-bold py-3 px-6 rounded-full w-full hover:shadow-lg transition-all">
                            Open Treasure
                        </button>
                    </div>
                </div>
            )}

            {/* Chest Modal */}
            {modalType === "chest" && (
                <div className="fixed inset-0 flex items-center justify-center z-50 animate-fadeIn">
                    <div className="bg-black bg-opacity-50 absolute inset-0" onClick={handleClose}></div>
                    <div className="bg-white rounded-2xl p-6 max-w-xs w-full mx-4 z-30 text-center animate-scaleIn">
                        <div className="w-32 h-32 mx-auto mb-4 text-8xl flex items-center justify-center animate-bounce">
                            📦
                        </div>
                        <h3 className="font-bold text-xl mb-2">You found a treasure chest!</h3>
                        <p className="text-gray-700 mb-4">Tap to open and collect your reward!</p>
                        <button
                            onClick={handleChestOpen}
                            className="bg-gradient-to-r from-yellow-400 to-yellow-600 text-white font-bold py-3 px-6 rounded-full w-full hover:shadow-lg transition-all">
                            Open Chest
                        </button>
                    </div>
                </div>
            )}

            {/* Reward Modal */}
            {modalType === "reward" && (
                <div className="fixed inset-0 flex items-center justify-center z-50 animate-fadeIn">
                    <div className="bg-black bg-opacity-50 absolute inset-0" onClick={handleClose}></div>
                    <div className="bg-white rounded-2xl p-6 max-w-xs w-full mx-4 z-30 text-center animate-scaleIn">
                        <div className="w-24 h-24 mx-auto mb-4 relative">
                            <div className="absolute inset-0 flex items-center justify-center">
                                <i className="fas fa-gift text-5xl text-yellow-500 animate-pulse"></i>
                            </div>
                            <div className="absolute inset-0 flex items-center justify-center">
                                <div className="w-full h-full rounded-full border-4 border-yellow-300 animate-ping opacity-75"></div>
                            </div>
                        </div>
                        <h3 className="font-bold text-xl mb-2 text-green-600">Congratulations!</h3>
                        <p className="text-gray-700 mb-4">
                            You collected <span className="font-bold text-yellow-600">{rewardAmount}</span> tokens!
                        </p>
                        <button
                            onClick={handleClose}
                            className="bg-gradient-to-r from-purple-500 to-indigo-600 text-white font-bold py-3 px-6 rounded-full w-full hover:shadow-lg transition-all">
                            Continue Hunting
                        </button>
                    </div>
                </div>
            )}

            <style jsx>{`
                @keyframes fadeIn {
                    from {
                        opacity: 0;
                    }
                    to {
                        opacity: 1;
                    }
                }

                @keyframes scaleIn {
                    from {
                        transform: scale(0.8);
                        opacity: 0;
                    }
                    to {
                        transform: scale(1);
                        opacity: 1;
                    }
                }

                .animate-fadeIn {
                    animation: fadeIn 0.3s ease-out;
                }

                .animate-scaleIn {
                    animation: scaleIn 0.3s ease-out;
                }
            `}</style>
        </>
    );
}
