// src/hooks/useGameActions.ts
import { useState, useCallback } from 'react';
import type { DatabaseUser, EnergyUsageResult, ExperienceResult } from '@/lib/supabase';

interface GameActionsState {
  loading: boolean;
  error: string | null;
}

interface UseGameActionsReturn {
  loading: boolean;
  error: string | null;
  useEnergy: (userId: number, amount?: number) => Promise<EnergyUsageResult>;
  addExperience: (userId: number, expAmount: number) => Promise<ExperienceResult>;
  updateBalance: (userId: number, amount: number) => Promise<{ success: boolean; new_balance: number }>;
  getUser: (userId: number) => Promise<DatabaseUser | null>;
  clearError: () => void;
}

export function useGameActions(): UseGameActionsReturn {
  const [state, setState] = useState<GameActionsState>({
    loading: false,
    error: null
  });

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  const makeRequest = useCallback(async (action: string, data: any) => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await fetch('/api/game/actions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      const result = await response.json();
      setState(prev => ({ ...prev, loading: false }));
      
      return result.result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setState(prev => ({ ...prev, loading: false, error: errorMessage }));
      throw error;
    }
  }, []);

  // Sử dụng năng lượng
  const useEnergy = useCallback(async (userId: number, amount: number = 1): Promise<EnergyUsageResult> => {
    try {
      const result = await makeRequest('use_energy', {
        type: 'use_energy',
        userId,
        amount
      });
      
      console.log('⚡ Energy used:', result);
      return result;
    } catch (error) {
      console.error('Error using energy:', error);
      return {
        success: false,
        current_energy: 0,
        message: error instanceof Error ? error.message : 'Failed to use energy'
      };
    }
  }, [makeRequest]);

  // Thêm kinh nghiệm
  const addExperience = useCallback(async (userId: number, expAmount: number): Promise<ExperienceResult> => {
    try {
      const result = await makeRequest('add_experience', {
        type: 'add_experience',
        userId,
        amount: expAmount
      });
      
      console.log('📈 Experience added:', result);
      return result;
    } catch (error) {
      console.error('Error adding experience:', error);
      return {
        success: false,
        new_level: 0,
        new_progress: 0,
        level_up: false
      };
    }
  }, [makeRequest]);

  // Cập nhật balance
  const updateBalance = useCallback(async (userId: number, amount: number): Promise<{ success: boolean; new_balance: number }> => {
    try {
      const result = await makeRequest('update_balance', {
        type: 'update_balance',
        userId,
        amount
      });
      
      console.log('💰 Balance updated:', result);
      return result;
    } catch (error) {
      console.error('Error updating balance:', error);
      return {
        success: false,
        new_balance: 0
      };
    }
  }, [makeRequest]);

  // Lấy thông tin user (với energy được cập nhật)
  const getUser = useCallback(async (userId: number): Promise<DatabaseUser | null> => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      const response = await fetch(`/api/game/actions?userId=${userId}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      const data = await response.json();
      setState(prev => ({ ...prev, loading: false }));
      
      console.log('👤 User data retrieved:', data.user);
      return data.user;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setState(prev => ({ ...prev, loading: false, error: errorMessage }));
      console.error('Error getting user:', error);
      return null;
    }
  }, []);

  return {
    loading: state.loading,
    error: state.error,
    useEnergy,
    addExperience,
    updateBalance,
    getUser,
    clearError
  };
}

// Helper hook để sử dụng cho game mechanics thường dùng
export function useGameMechanics(userId: number | null) {
  const gameActions = useGameActions();
  
  // Thực hiện một action game hoàn chỉnh (dùng energy + nhận exp + có thể nhận token)
  const performGameAction = useCallback(async (params: {
    energyCost?: number;
    expReward?: number;
    tokenReward?: number;
  }) => {
    if (!userId) {
      throw new Error('User ID is required');
    }

    const { energyCost = 1, expReward = 0, tokenReward = 0 } = params;
    const results: any = {};

    try {
      // 1. Sử dụng năng lượng (nếu cần)
      if (energyCost > 0) {
        const energyResult = await gameActions.useEnergy(userId, energyCost);
        results.energy = energyResult;
        
        if (!energyResult.success) {
          throw new Error(energyResult.message);
        }
      }

      // 2. Thêm kinh nghiệm (nếu có)
      if (expReward > 0) {
        const expResult = await gameActions.addExperience(userId, expReward);
        results.experience = expResult;
      }

      // 3. Thêm token (nếu có)
      if (tokenReward > 0) {
        const balanceResult = await gameActions.updateBalance(userId, tokenReward);
        results.balance = balanceResult;
      }

      return {
        success: true,
        results
      };

    } catch (error) {
      console.error('Game action failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Game action failed',
        results
      };
    }
  }, [userId, gameActions]);

  return {
    ...gameActions,
    performGameAction
  };
}