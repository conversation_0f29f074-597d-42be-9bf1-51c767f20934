// src/store/gameStore.ts
import { EggType, GameEgg, GamePosition, GameStore } from "@/types/game";
import { createStore } from "./store.factory";

const eggTypes: EggType[] = [
    { type: "bronze", color: "#CD7F32", tokenValue: 10 },
    { type: "silver", color: "#C0C0C0", tokenValue: 25 },
    { type: "gold", color: "#FFD700", tokenValue: 50 }
];

const generateRandomPosition = (center: GamePosition): GamePosition => {
    const offset = 0.005;
    return {
        lat: center.lat + Math.random() * offset * 2 - offset,
        lng: center.lng + Math.random() * offset * 2 - offset
    };
};

const generateEggs = (userPosition: GamePosition): GameEgg[] => {
    const eggs: GameEgg[] = [];
    for (let i = 0; i < 15; i++) {
        const eggType = eggTypes[Math.floor(Math.random() * eggTypes.length)];
        const position = generateRandomPosition(userPosition);

        eggs.push({
            id: `egg-${Date.now()}-${i}`,
            position,
            eggType,
            collected: false
        });
    }
    return eggs;
};

export const useGameStore = createStore<GameStore>("gameStore", (set, get) => ({
    // Initial state - these are fallback values, real data comes from Supabase via realtime
    tokens: 0, // Will be overridden by Supabase user.balance
    level: 1, // Will be overridden by Supabase user.level
    xp: 0, // Will be overridden by Supabase user.level_progress
    nextLevelXp: 100, // Fixed at 100 for database system
    isPlaying: false,
    collectedEggs: 0, // Local tracking, can be synced with database later
    eggs: [],
    energy: 0, // Will be overridden by Supabase user.energy
    maxEnergy: 50, // Will be overridden by Supabase user.energy_total
    huntTimer: null,
    energyTimer: null,
    huntEndTime: null,
    userPosition: null,

    // Realtime sync actions with Supabase data
    syncUserData: (userData) => {
        const currentState = get();
        
        // Only update if values actually changed to prevent unnecessary re-renders
        if (
            currentState.tokens !== userData.balance ||
            currentState.level !== userData.level ||
            currentState.xp !== userData.level_progress ||
            currentState.energy !== userData.energy ||
            currentState.maxEnergy !== userData.energy_total
        ) {
            console.log('🔄 Syncing user data to game store:', userData);
            set({
                tokens: userData.balance,
                level: userData.level,
                xp: userData.level_progress,
                energy: userData.energy,
                maxEnergy: userData.energy_total
            });
        }
    },

    // Actions
    setUserPosition: (position: GamePosition) => {
        const currentState = get();
        set({ userPosition: position });
        
        // Only spawn eggs if we don't have any uncollected eggs
        if (currentState.eggs.filter(egg => !egg.collected).length === 0) {
            const newEggs = generateEggs(position);
            set({ eggs: newEggs });
        }
    },

    spawnEggs: () => {
        const { userPosition, eggs } = get();
        if (!userPosition) return;

        // Check if we already have uncollected eggs to prevent duplicates
        const uncollectedEggs = eggs.filter(egg => !egg.collected);
        if (uncollectedEggs.length >= 10) return; // Don't spawn if we already have enough

        const newEggs = generateEggs(userPosition);
        set({ eggs: newEggs });
    },

    startHunt: () => {
        const state = get();
        if (state.energy <= 0) return;

        // Note: Energy deduction is now handled by Supabase in the API call
        // This just starts the local hunt timer and UI state
        set({
            isPlaying: true,
            huntEndTime: Date.now() + 20 * 60 * 1000 // 20 minutes
        });

        // Start hunt timer
        const huntTimer = setInterval(() => {
            const { updateHuntTimer } = get();
            updateHuntTimer();
        }, 1000) as NodeJS.Timeout;

        set({ huntTimer });
    },

    endHunt: () => {
        const { huntTimer } = get();
        if (huntTimer) {
            clearInterval(huntTimer);
        }

        set({
            isPlaying: false,
            huntTimer: null,
            huntEndTime: null
        });
    },

    updateHuntTimer: () => {
        const { huntEndTime, endHunt } = get();
        if (!huntEndTime) return;

        const remaining = huntEndTime - Date.now();
        if (remaining <= 0) {
            endHunt();
        }
    },

    collectEgg: (eggId: string) => {
        const state = get();
        const egg = state.eggs.find((e: GameEgg) => e.id === eggId);
        if (!egg || egg.collected) return;

        const updatedEggs = state.eggs.map((e: GameEgg) => (e.id === eggId ? { ...e, collected: true } : e));

        set({
            eggs: updatedEggs,
            collectedEggs: state.collectedEggs + 1
        });

        // Note: Token and XP rewards are now handled by Supabase
        // This just updates the local state for immediate UI feedback
        // Real rewards are processed in the handleEggOpen function in EggHuntGame
        // and synced back via realtime

        // Spawn new eggs after delay if we're running low
        setTimeout(() => {
            const currentState = get();
            const uncollectedEggs = currentState.eggs.filter(e => !e.collected);
            if (uncollectedEggs.length < 5) { // Spawn new eggs if less than 5 left
                const { spawnEggs } = get();
                spawnEggs();
            }
        }, 3000);
    },

    // Local state updates for immediate UI feedback
    // Real data updates happen through syncUserData when Supabase realtime events fire
    addTokens: (amount: number) => {
        const { tokens } = get();
        set({ tokens: tokens + amount });
    },

    addXP: (amount: number) => {
        const state = get();
        const newXP = state.xp + amount;

        if (newXP >= state.nextLevelXp) {
            // Level up - local state update for immediate feedback
            set({
                level: state.level + 1,
                xp: newXP - state.nextLevelXp
            });
        } else {
            set({ xp: newXP });
        }
    },

    // Energy recharge is now handled by Supabase automatically via realtime
    // This is kept for compatibility but real energy updates come from database
    rechargeEnergy: () => {
        const state = get();
        if (state.energy < state.maxEnergy) {
            set({ energy: state.energy + 1 });
        }
    },

    // Update energy from external source (Supabase realtime)
    updateEnergy: (newEnergy: number) => {
        const currentEnergy = get().energy;
        if (currentEnergy !== newEnergy) {
            console.log('⚡ Energy updated via realtime:', newEnergy);
            set({ energy: newEnergy });
        }
    },

    // Update tokens from external source (Supabase realtime)
    updateTokens: (newTokens: number) => {
        const currentTokens = get().tokens;
        if (currentTokens !== newTokens) {
            console.log('💰 Tokens updated via realtime:', newTokens);
            set({ tokens: newTokens });
        }
    },

    // Update level data from external source (Supabase realtime)
    updateLevel: (newLevel: number, newProgress: number) => {
        const state = get();
        if (state.level !== newLevel || state.xp !== newProgress) {
            console.log('📈 Level updated via realtime:', { level: newLevel, progress: newProgress });
            set({ 
                level: newLevel, 
                xp: newProgress 
            });
        }
    }
}));