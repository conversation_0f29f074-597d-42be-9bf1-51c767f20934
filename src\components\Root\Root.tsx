// src/components/Root/Root.tsx
"use client";

import { initData, miniApp, viewport, useLaunchParams, useSignal, postEvent } from "@telegram-apps/sdk-react";
import { AppRoot } from "@telegram-apps/telegram-ui";
import { TonConnectUIProvider } from "@tonconnect/ui-react";
import { type PropsWithChildren, useEffect, useState } from "react";

import { ErrorBoundary } from "@/components/ErrorBoundary";
import { ErrorPage } from "@/components/ErrorPage";
import { LoadingScreen } from "@/components/LoadingScreen/LoadingScreen";

import { useDidMount } from "@/hooks/useDidMount";
import { useUserSync } from "@/hooks/useUserSync"; // Import hook để sync user data
import { clientLocaleManager } from "@/core/i18n/client-locale";

import "./styles.css";

function RootInner({ children }: PropsWithChildren) {
    const lp = useLaunchParams();
    const [isAppReady, setIsAppReady] = useState(false);

    const isDark = useSignal(miniApp.isDark);
    const initDataUser = useSignal(initData.user);
    
    // Sử dụng hook để tự động sync user data với Supabase
    const userSync = useUserSync();

    // Auto-detect and set locale from Telegram user data (client-side only)
    useEffect(() => {
        if (initDataUser?.language_code) {
            console.log('Detecting language from Telegram:', initDataUser.language_code);
            
            // Set locale using client-side manager - NO SERVER CALLS, NO RELOAD
            const detectedLocale = clientLocaleManager.setTelegramLocale(initDataUser.language_code);
            console.log('Client locale set to:', detectedLocale);
            
            // Log locale source for debugging
            const localeSource = clientLocaleManager.getLocaleSource();
            console.log('Locale source:', localeSource);
        }
    }, [initDataUser]);

    // Log user sync status để debug
    useEffect(() => {
        if (userSync.loading) {
            console.log('📡 Syncing user data to Supabase...');
        } else if (userSync.error) {
            console.error('❌ User sync error:', userSync.error);
        } else if (userSync.user) {
            console.log(`✅ User ${userSync.action}:`, {
                id: userSync.user.id,
                name: `${userSync.user.first_name} ${userSync.user.last_name || ''}`.trim(),
                username: userSync.user.username,
                action: userSync.action
            });
        }
    }, [userSync]);

    // Configure viewport, fullscreen, header and swipe behavior when component mounts
    useEffect(() => {
        const initializeApp = async () => {
            try {
                // 1. Setup swipe behavior - DISABLE swipe to close
                console.log('Setting up swipe behavior...');
                try {
                    postEvent('web_app_setup_swipe_behavior', { 
                        allow_vertical_swipe: false  // Ngăn swipe vertical để close app
                    });
                    console.log('Swipe behavior configured successfully');
                } catch (swipeError) {
                    console.warn('Could not configure swipe behavior:', swipeError);
                }

                // 2. Request fullscreen mode (available since v8.0)
                console.log('Requesting fullscreen mode...');
                try {
                    postEvent('web_app_request_fullscreen');
                } catch (fullscreenError) {
                    console.warn('Could not request fullscreen:', fullscreenError);
                }
                
                // 3. Expand viewport to maximum
                console.log('Expanding viewport...');
                try {
                    viewport.expand();
                } catch (viewportError) {
                    console.warn('Could not expand viewport:', viewportError);
                }
                
                // 4. Set header color with HuntFi branding
                try {
                    miniApp.setHeaderColor('#4F46E5');
                } catch (headerError) {
                    console.warn('Could not set header color:', headerError);
                }
                
                // 5. Setup closing behavior
                try {
                    postEvent('web_app_setup_closing_behavior', { 
                        need_confirmation: false  // Không cần confirmation khi close
                    });
                } catch (closingError) {
                    console.warn('Could not configure closing behavior:', closingError);
                }

                // 6. Setup back button behavior if needed
                try {
                    postEvent('web_app_setup_back_button', { 
                        is_visible: false  // Ẩn back button mặc định
                    });
                } catch (backButtonError) {
                    console.warn('Could not configure back button:', backButtonError);
                }
                
                // 7. Notify that app is ready
                miniApp.ready();
                
                console.log('HuntFi app initialization complete');
                
            } catch (e) {
                console.warn('Failed to configure app:', e);
                // Even if some configs fail, still mark app as ready
                try {
                    miniApp.ready();
                } catch (readyError) {
                    console.warn('Failed to mark app as ready:', readyError);
                }
            }
        };

        // Small delay to ensure Telegram is ready
        const timer = setTimeout(initializeApp, 100);
        return () => clearTimeout(timer);
    }, []);

    const handleLoadingComplete = () => {
        setIsAppReady(true);
    };

    return (
        <TonConnectUIProvider manifestUrl="/tonconnect-manifest.json">
            {/* HuntFi LoadingScreen - only one loading screen needed */}
            {!isAppReady && (
                <LoadingScreen 
                    onLoadingComplete={handleLoadingComplete}
                    minLoadingTime={2500}
                    showProgress={true}
                />
            )}
            
            {/* App content với smooth transition */}
            <div className={`transition-all duration-700 ease-out ${
                isAppReady ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
            }`}>
                <AppRoot
                    appearance={isDark ? "dark" : "light"}
                    platform={["macos", "ios"].includes(lp.tgWebAppPlatform) ? "ios" : "base"}
                    className="root-inner">
                    <div className="app-container min-h-screen">
                        {children}
                    </div>
                </AppRoot>
            </div>
        </TonConnectUIProvider>
    );
}

export function Root(props: PropsWithChildren) {
    // Use didMount to ensure client-side hydration
    const didMount = useDidMount();

    return didMount ? (
        <ErrorBoundary fallback={ErrorPage}>
            <RootInner {...props} />
        </ErrorBoundary>
    ) : (
        // Minimal server-side fallback - no redundant loading screen
        <div className="root__loading min-h-screen" style={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 25%, #8b5cf6 50%, #ec4899 75%, #f59e0b 100%)'
        }}>
            {/* Empty - client will take over immediately */}
        </div>
    );
}