// src/mockEnv.ts
import { mockTelegramEnv, isTMA, emitEvent } from '@telegram-apps/sdk-react';

// It is important, to mock the environment only for development purposes. When building the
// application, the code inside will be tree-shaken, so you will not see it in your final bundle.
export async function mockEnv(): Promise<void> {
  return process.env.NODE_ENV !== 'development'
    ? undefined
    : isTMA('complete').then((isTma) => {
        if (!isTma) {
          const themeParams = {
            // Màu chính - xanh dương sáng thay vì xanh xám
            accent_text_color: '#007AFF',
            // Nền chính - trắng thay vì đen
            bg_color: '#ffffff',
            // Nút - xanh dương đậm hơn
            button_color: '#007AFF',
            button_text_color: '#ffffff',
            // <PERSON><PERSON>u cảnh báo - đỏ nhẹ hơn
            destructive_text_color: '#ff3b30',
            // Header - trắng với shadow nhẹ
            header_bg_color: '#f8f9fa',
            // Gợi ý - xám nhẹ
            hint_color: '#8e8e93',
            // Link - xanh dương
            link_color: '#007AFF',
            // Nền phụ - xám rất nhẹ
            secondary_bg_color: '#f2f2f7',
            // Nền section - trắng
            section_bg_color: '#ffffff',
            // Header section - xanh dương
            section_header_text_color: '#007AFF',
            // Subtitle - xám
            subtitle_text_color: '#8e8e93',
            // Text chính - đen
            text_color: '#000000',
          } as const;

          const noInsets = { left: 0, top: 0, bottom: 0, right: 0 } as const;

          mockTelegramEnv({
            onEvent(e) {
              // Here you can write your own handlers for all known Telegram Mini Apps methods.
              console.log('[Mock] Handling event:', e);

              // Handle swipe behavior setup - KEY ADDITION for your request
              if (e[0] === 'web_app_setup_swipe_behavior') {
                const params = e[1] as { allow_vertical_swipe?: boolean };
                console.log('[Mock] Swipe behavior configured:', params);
                // In real app, this would control swipe-to-close behavior
                return;
              }

              // Handle closing behavior setup
              if (e[0] === 'web_app_setup_closing_behavior') {
                const params = e[1] as { need_confirmation?: boolean };
                console.log('[Mock] Closing behavior configured:', params);
                return;
              }

              // Handle back button setup
              if (e[0] === 'web_app_setup_back_button') {
                const params = e[1] as { is_visible?: boolean };
                console.log('[Mock] Back button configured:', params);
                return;
              }

              // Handle fullscreen request
              if (e[0] === 'web_app_request_fullscreen') {
                console.log('[Mock] Fullscreen requested');
                return;
              }

              // Handle theme request
              if (e[0] === 'web_app_request_theme') {
                return emitEvent('theme_changed', { theme_params: themeParams });
              }

              // Handle viewport request
              if (e[0] === 'web_app_request_viewport') {
                return emitEvent('viewport_changed', {
                  height: window.innerHeight,
                  width: window.innerWidth,
                  is_expanded: true,
                  is_state_stable: true,
                });
              }

              // Handle content safe area request
              if (e[0] === 'web_app_request_content_safe_area') {
                return emitEvent('content_safe_area_changed', noInsets);
              }

              // Handle safe area request
              if (e[0] === 'web_app_request_safe_area') {
                return emitEvent('safe_area_changed', noInsets);
              }

              // Handle setHeaderColor
              if (e[0] === 'web_app_set_header_color') {
                // Trong môi trường mock, chúng ta không cần làm gì
                // nhưng có thể log để debug
                console.log('[Mock] Header color set to:', e[1]);
                return;
              }

              // Handle expand viewport
              if (e[0] === 'web_app_expand') {
                return emitEvent('viewport_changed', {
                  height: window.innerHeight,
                  width: window.innerWidth,
                  is_expanded: true,
                  is_state_stable: true,
                });
              }

              // Handle ready
              if (e[0] === 'web_app_ready') {
                console.log('[Mock] Mini App is ready');
                return;
              }

              // Handle setup_main_button
              if (e[0] === 'web_app_setup_main_button') {
                console.log('[Mock] Main button setup:', e[1]);
                return;
              }

              // Handle haptic feedback
              if (e[0] === 'web_app_trigger_haptic_feedback') {
                console.log('[Mock] Haptic feedback triggered:', e[1]);
                return;
              }
            },
            launchParams: new URLSearchParams([
              // Discover more launch parameters:
              // https://docs.telegram-mini-apps.com/platform/launch-parameters#parameters-list
              ['tgWebAppThemeParams', JSON.stringify(themeParams)],
              // Your init data goes here. Learn more about it here:
              // https://docs.telegram-mini-apps.com/platform/init-data#parameters-list
              //
              // Note that to make sure, you are using a valid init data, you must pass it exactly as it
              // is sent from the Telegram application. The reason is in case you will sort its keys
              // (auth_date, hash, user, etc.) or values your own way, init data validation will more
              // likely to fail on your server side. So, to make sure you are working with a valid init
              // data, it is better to take a real one from your application and paste it here. It should
              // look something like this (a correctly encoded URL search params):
              // ```
              // user=%7B%22id%22%3A279058397%2C%22first_name%22%3A%22Vladislav%22%2C%22last_name%22...
              // ```
              // But in case you don't really need a valid init data, use this one:
              ['tgWebAppData', new URLSearchParams([
                ['auth_date', (new Date().getTime() / 1000 | 0).toString()],
                ['hash', 'some-hash'],
                ['signature', 'some-signature'],
                ['user', JSON.stringify({ 
                  id: 1, 
                  first_name: 'Test',
                  last_name: 'User',
                  username: 'testuser', 
                  photo_url: 'https://imageio.forbes.com/specials-images/imageserve/6170e01f8d7639b95a7f2eeb/Sotheby-s-NFT-Natively-Digital-1-2-sale-Bored-Ape-Yacht-Club--8817-by-Yuga-Labs/0x0.png?format=png&width=960'
                })],
              ]).toString()],
              ['tgWebAppVersion', '8.4'],
              ['tgWebAppPlatform', 'tdesktop'],
            ]),
          });

          console.warn(
            '⚠️ As long as the current environment was not considered as the Telegram-based one, it was mocked. Take a note, that you should not do it in production and current behavior is only specific to the development process. Environment mocking is also applied only in development mode. So, after building the application, you will not see this behavior and related warning, leading to crashing the application outside Telegram.',
          );
        }
      });
}