// src/components/game/EggHuntGame.tsx
"use client";

import { useEffect, useState } from "react";

import { useGameMechanics } from "@/hooks/useGameActions";
import { useHashRouting } from "@/hooks/useHashRouting";
import { useUserSync } from "@/hooks/useUserSync";
import { useGameStore } from "@/store/gameStore";
import { GameEgg } from "@/types/game";

import { BottomNavigation } from "./BottomNavigation";
import { GameHeader } from "./GameHeader";
import { GameMap } from "./GameMap";
import { GameModals } from "./GameModals";
import { LeaderboardSection, ProfileSection, ReferSection, TasksSection } from "./GameSections";

export function EggHuntGame() {
    const { activeTab } = useHashRouting();
    const [selectedEgg, setSelectedEgg] = useState<GameEgg | null>(null);
    const [huntTimer, setHuntTimer] = useState<string>("");

    const {
        isPlaying,
        huntEndTime,
        startHunt,
        endHunt,
        collectEgg,
        addTokens,
        energy,
        syncUserData,
        updateEnergy,
        updateTokens,
        updateLevel
    } = useGameStore();
    const { user, refreshUser } = useUserSync();
    const { useEnergy: consumeEnergy, addExperience, updateBalance } = useGameMechanics(user?.id || null);

    // Sync user data from Supabase to game store
    useEffect(() => {
        if (user) {
            syncUserData({
                balance: user.balance,
                level: user.level,
                level_progress: user.level_progress,
                energy: user.energy,
                energy_total: user.energy_total
            });
        }
    }, [user, syncUserData]);

    useEffect(() => {
        if (isPlaying && huntEndTime) {
            const interval = setInterval(() => {
                const now = Date.now();
                const remaining = huntEndTime - now;

                if (remaining <= 0) {
                    setHuntTimer("");
                    return;
                }

                const minutes = Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((remaining % (1000 * 60)) / 1000);
                setHuntTimer(`${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`);
            }, 1000);

            return () => clearInterval(interval);
        } else {
            setHuntTimer("");
        }
    }, [isPlaying, huntEndTime]);

    const handleStartHunt = async () => {
        // Check energy from database if user is available
        const currentEnergy = user ? user.energy : energy;

        if (currentEnergy <= 0) {
            alert("Not enough energy! Please wait for energy to recharge.");
            return;
        }

        // Use energy from database if user is synced
        if (user) {
            try {
                const result = await consumeEnergy(user.id, 1);
                if (!result.success) {
                    alert(result.message);
                    return;
                }

                // Update game store energy
                updateEnergy(result.current_energy);

                console.log("Energy used:", result);
            } catch (error) {
                console.error("Failed to use energy:", error);
                alert("Failed to start hunt. Please try again.");
                return;
            }
        }

        // Start the hunt
        startHunt();
    };

    const handleEggCollect = (eggId: string) => {
        if (!isPlaying) return;

        const eggs = useGameStore.getState().eggs;
        const egg = eggs.find((e: GameEgg) => e.id === eggId);
        if (egg) {
            setSelectedEgg(egg);
        }
    };

    const handleEggOpen = async (tokens: number) => {
        if (selectedEgg) {
            // Collect egg in game store (immediate UI feedback)
            collectEgg(selectedEgg.id);
            addTokens(tokens);

            // Update database if user is synced
            if (user) {
                try {
                    // Add tokens to balance
                    await updateBalance(user.id, tokens);

                    // Add experience based on egg type
                    let expReward = 5; // Base EXP
                    if (selectedEgg.eggType.type === "silver") {
                        expReward = 10;
                    } else if (selectedEgg.eggType.type === "gold") {
                        expReward = 20;
                    }

                    const expResult = await addExperience(user.id, expReward);

                    // Update game store with new values from database
                    updateTokens(user.balance + tokens);
                    updateLevel(expResult.new_level, expResult.new_progress);

                    if (expResult.level_up) {
                        // Show level up notification
                        setTimeout(() => {
                            alert(`Congratulations! You reached level ${expResult.new_level}!`);
                        }, 1000);
                    }

                    // Refresh user data to ensure sync
                    setTimeout(() => {
                        refreshUser();
                    }, 500);

                    console.log("Rewards updated:", { tokens, exp: expReward, levelUp: expResult.level_up });
                } catch (error) {
                    console.error("Failed to update rewards:", error);
                }
            }
        }
    };

    const handleModalClose = () => {
        setSelectedEgg(null);
    };

    const renderContent = () => {
        switch (activeTab) {
            case "hunt":
                return (
                    <div className="fixed inset-0 w-full h-full">
                        <div className="absolute top-0 left-0 right-0 z-50">
                            <GameHeader />
                        </div>

                        <div className="w-full h-full">
                            <GameMap onEggCollect={handleEggCollect} />
                        </div>

                        <div className="fixed bottom-0 left-0 right-0 z-40">
                            <BottomNavigation />
                        </div>

                        <button
                            onClick={isPlaying ? endHunt : handleStartHunt}
                            disabled={!isPlaying && (user ? user.energy <= 0 : energy <= 0)}
                            className={`fixed bottom-32 left-1/2 transform -translate-x-1/2 font-bold py-3 px-8 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 z-50 ${isPlaying
                                    ? "bg-gradient-to-r from-red-500 to-red-600 text-white"
                                    : (user ? user.energy <= 0 : energy <= 0)
                                        ? "bg-gray-400 text-gray-200 cursor-not-allowed"
                                        : "bg-gradient-to-r from-purple-500 to-indigo-600 text-white"
                                }`}>
                            {isPlaying ? (
                                <>
                                    <i className="fas fa-stop mr-2"></i>
                                    Stop Hunting {huntTimer && `(${huntTimer})`}
                                </>
                            ) : (
                                <>
                                    <i className="fas fa-play mr-2"></i>
                                    {(user ? user.energy <= 0 : energy <= 0) ? "No Energy" : "Start Hunting"}
                                </>
                            )}
                        </button>
                    </div>
                );

            case "tasks":
                return <TasksSection />;

            case "leaderboard":
                return <LeaderboardSection />;

            case "refer":
                return <ReferSection />;

            case "profile":
                return <ProfileSection />;

            default:
                return <div className="flex items-center justify-center h-full">Content not found</div>;
        }
    };

    return (
        <div className="flex flex-col h-screen bg-gradient-to-br from-blue-50 to-purple-50">
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />

            {activeTab !== "hunt" && activeTab !== "profile" && <GameHeader />}
            <main className={`flex-1 relative overflow-auto ${activeTab === "hunt" ? "" : ""}`}>{renderContent()}</main>

            {activeTab !== "hunt" && <BottomNavigation />}

            <GameModals selectedEgg={selectedEgg} onEggOpen={handleEggOpen} onClose={handleModalClose} />
        </div>
    );
}
