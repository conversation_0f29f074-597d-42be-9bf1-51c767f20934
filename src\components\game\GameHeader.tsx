// src/components/game/GameHeader.tsx
"use client";

import { initData, useSignal } from "@telegram-apps/sdk-react";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";

import { useEnergyTimer } from "@/hooks/useEnergyTimer";
import { useUserSync } from "@/hooks/useUserSync";
import { useGameStore } from "@/store/gameStore";
import Image from "next/image";

export function GameHeader() {
    const t = useTranslations();
    const { tokens, level, xp, nextLevelXp, energy, maxEnergy, collectedEggs, syncUserData } = useGameStore();
    const { user } = useUserSync();
    const energyTimer = useEnergyTimer(user);
    const [safeAreaTop, setSafeAreaTop] = useState(44); // Default safe area

    // Get user info from Telegram
    const initDataUser = useSignal(initData.user);

    // Sync user data from Supabase to game store via realtime
    useEffect(() => {
        if (user) {
            syncUserData({
                balance: user.balance,
                level: user.level,
                level_progress: user.level_progress,
                energy: user.energy,
                energy_total: user.energy_total
            });
        }
    }, [user, syncUserData]);

    // Use database data if available, fallback to game store
    const displayData = user
        ? {
              tokens: user.balance,
              level: user.level,
              levelProgress: user.level_progress,
              energy: user.energy,
              maxEnergy: user.energy_total,
              collectedEggs: collectedEggs, // Local tracking
              firstName: user.first_name,
              lastName: user.last_name,
              photoUrl: user.photo_url
          }
        : {
              tokens: tokens,
              level: level,
              levelProgress: 0,
              energy: energy,
              maxEnergy: maxEnergy,
              collectedEggs: collectedEggs,
              firstName: initDataUser?.first_name || "Guest",
              lastName: initDataUser?.last_name,
              photoUrl: initDataUser?.photo_url
          };

    const progressPercent = user
        ? (displayData.levelProgress / 100) * 100 // Database uses 0-100 scale
        : (xp / nextLevelXp) * 100; // Game store uses xp/nextLevelXp

    // Safe area detection
    useEffect(() => {
        const updateSafeArea = () => {
            if (typeof window !== "undefined") {
                try {
                    // Try to get CSS env() value
                    const testElement = document.createElement("div");
                    testElement.style.paddingTop = "env(safe-area-inset-top)";
                    document.body.appendChild(testElement);
                    const computed = getComputedStyle(testElement);
                    const safeTop = computed.paddingTop;
                    document.body.removeChild(testElement);

                    const topValue = parseInt(safeTop.replace("px", "")) || 0;

                    // Set minimum safe area based on device
                    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
                    const minSafeArea = isIOS ? 64 : 44; // Increase even more for full clearance

                    setSafeAreaTop(Math.max(topValue, minSafeArea));
                } catch (error) {
                    // Fallback for older browsers - increase fallback values more
                    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
                    setSafeAreaTop(isIOS ? 64 : 44);
                }
            }
        };

        updateSafeArea();

        // Listen for orientation changes
        window.addEventListener("orientationchange", () => {
            setTimeout(updateSafeArea, 100);
        });
        window.addEventListener("resize", updateSafeArea);

        return () => {
            window.removeEventListener("orientationchange", updateSafeArea);
            window.removeEventListener("resize", updateSafeArea);
        };
    }, []);

    const getUserInfo = () => {
        const firstName = displayData.firstName;
        const lastName = displayData.lastName;
        const photoUrl = displayData.photoUrl;

        const displayName = `${firstName}${lastName ? ` ${lastName}` : ""}`;
        const initials = firstName ? (lastName ? `${firstName[0]}${lastName[0]}` : firstName.slice(0, 2)) : "HF";

        return {
            displayName,
            initials: initials.toUpperCase(),
            photoUrl
        };
    };

    const { displayName, initials, photoUrl } = getUserInfo();

    return (
        <header
            className="relative"
            style={{
                paddingTop: `${safeAreaTop + 35}px`, // Add 35px extra padding for better clearance
                zIndex: 1000
            }}>
            {/* Background gradient with HuntFi GameFi theme */}
            <div className="absolute inset-0 bg-gradient-to-br from-purple-600 via-indigo-600 to-blue-600 shadow-lg rounded-b-2xl overflow-hidden">
                {/* Animated background elements for GameFi feel */}
                <div className="absolute inset-0 opacity-20">
                    <div className="absolute top-4 left-4 w-8 h-8 rounded-full bg-yellow-400"></div>
                    <div className="absolute top-8 right-8 w-6 h-6 rounded-full bg-green-400 "></div>
                    <div className="absolute bottom-4 left-1/3 w-4 h-4 rounded-full bg-pink-400"></div>
                </div>
            </div>

            {/* Content */}
            <div className="relative z-10 px-4 pt-3 pb-4">
                {/* Row 1: User info và crypto tokens */}
                <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                        <div className="relative">
                            {/* Avatar with image from Telegram or initials */}
                            <div className="relative w-14 h-14">
                                {/* Level progress circle with GameFi styling */}
                                <svg className="w-full h-full transform -rotate-90" viewBox="0 0 36 36">
                                    <path
                                        d="M18 2.0845
                                            a 15.9155 15.9155 0 0 1 0 31.831
                                            a 15.9155 15.9155 0 0 1 0 -31.831"
                                        fill="none"
                                        stroke="rgba(255,255,255,0.3)"
                                        strokeWidth="2"
                                    />
                                    <path
                                        d="M18 2.0845
                                            a 15.9155 15.9155 0 0 1 0 31.831
                                            a 15.9155 15.9155 0 0 1 0 -31.831"
                                        fill="none"
                                        stroke="url(#huntfi-gradient)"
                                        strokeWidth="2"
                                        strokeDasharray={`${progressPercent}, 100`}
                                        className="transition-all duration-500"
                                    />
                                    <defs>
                                        <linearGradient id="huntfi-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                            <stop offset="0%" stopColor="#fbbf24" />
                                            <stop offset="50%" stopColor="#f59e0b" />
                                            <stop offset="100%" stopColor="#d97706" />
                                        </linearGradient>
                                    </defs>
                                </svg>

                                {/* Avatar */}
                                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-12 h-12 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center text-white text-xl font-bold shadow-lg overflow-hidden border-2 border-white/30">
                                    {photoUrl ? (
                                        <Image
                                            src={photoUrl}
                                            alt={displayName}
                                            width={48}
                                            height={48}
                                            className="w-full h-full object-cover"
                                        />
                                    ) : (
                                        <span>{initials}</span>
                                    )}
                                </div>

                                {/* Level badge */}
                                <div className="absolute -bottom-1 -right-1 bg-gradient-to-r from-yellow-500 to-orange-500 text-white text-xs font-bold w-6 h-6 rounded-full flex items-center justify-center shadow-md border border-white/50">
                                    {displayData.level}
                                </div>

                                {/* GameFi Hunter badge on top */}
                                <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-purple-500 to-pink-500 text-white text-xs font-bold px-2 py-0.5 rounded-full flex items-center shadow-md">
                                    <i className="fas fa-gem mr-0.5 text-xs"></i>
                                    GOLD
                                </div>
                            </div>
                        </div>

                        <div>
                            {/* User name from Telegram/Database */}
                            <h1 className="font-bold text-white text-lg leading-tight">{displayName}</h1>
                            {/* Found Treasure with GameFi terminology */}
                            <div className="flex items-center text-white/90 text-sm">
                                <i className="fas fa-treasure-chest mr-1.5 text-yellow-300"></i>
                                <span>
                                    {t("game.treasuresFound")}: {displayData.collectedEggs}
                                </span>
                            </div>
                            <div className="flex items-center text-white/90 text-sm">
                                <i className="fas fa-bolt text-yellow-400"></i>
                                <span className="ml-1">
                                    {t("game.energy")}: {displayData.energy}/{displayData.maxEnergy}
                                </span>
                                {energyTimer.isRegenerating && (
                                    <span className="ml-1 text-xs">({energyTimer.timeToNextEnergy})</span>
                                )}
                            </div>
                            {/* Energy regeneration info */}
                            {energyTimer.isRegenerating && (
                                <div className="text-white/70 text-xs">⚡ Full in: {energyTimer.timeToFullEnergy}</div>
                            )}
                        </div>
                    </div>

                    {/* Crypto Tokens Display */}
                    <div className="bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 flex items-center space-x-2 shadow-lg border border-white/30">
                        <div className="relative">
                            <i className="fas fa-coins text-yellow-400 text-lg"></i>
                            {/* Animated sparkle effect */}
                            <div className="absolute -top-1 -right-1 w-2 h-2 bg-yellow-300 rounded-full animate-ping"></div>
                        </div>
                        <span className="font-bold text-white text-xl">{displayData.tokens.toLocaleString()}</span>
                        <span className="text-white/80 text-xs font-medium">HUFI</span>
                    </div>
                </div>
            </div>
        </header>
    );
}
