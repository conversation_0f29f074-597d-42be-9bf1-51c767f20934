// src/app/api/game/actions/route.ts
import { addExperience, useEnergy as consumeEnergy, getUserWithUpdatedEnergy, updateBalance } from "@/lib/supabase";
import { NextRequest, NextResponse } from "next/server";

// Interface cho các loại action
interface GameAction {
    type: "use_energy" | "add_experience" | "update_balance" | "get_user";
    userId: number;
    amount?: number;
}

export async function POST(request: NextRequest) {
    try {
        const body: GameAction = await request.json();

        console.log("🎮 Game action received:", body);

        // Validate đầu vào
        if (!body.type || !body.userId) {
            return NextResponse.json({ error: "Missing required fields: type and userId" }, { status: 400 });
        }

        const { type, userId, amount = 1 } = body;

        switch (type) {
            case "use_energy": {
                const result = await consumeEnergy(userId, amount);
                return NextResponse.json({
                    success: true,
                    action: "use_energy",
                    result
                });
            }

            case "add_experience": {
                const result = await addExperience(userId, amount);
                return NextResponse.json({
                    success: true,
                    action: "add_experience",
                    result
                });
            }

            case "update_balance": {
                const success = await updateBalance(userId, amount);
                if (!success) {
                    return NextResponse.json({ error: "Failed to update balance" }, { status: 500 });
                }

                // Lấy thông tin user cập nhật
                const updatedUser = await getUserWithUpdatedEnergy(userId);
                return NextResponse.json({
                    success: true,
                    action: "update_balance",
                    result: {
                        success,
                        new_balance: updatedUser?.balance || 0
                    }
                });
            }

            case "get_user": {
                const user = await getUserWithUpdatedEnergy(userId);
                if (!user) {
                    return NextResponse.json({ error: "User not found" }, { status: 404 });
                }

                return NextResponse.json({
                    success: true,
                    action: "get_user",
                    result: user
                });
            }

            default:
                return NextResponse.json({ error: `Unknown action type: ${type}` }, { status: 400 });
        }
    } catch (error) {
        console.error("Error in game actions API:", error);
        return NextResponse.json({ error: "Internal server error" }, { status: 500 });
    }
}

// GET endpoint để lấy thông tin user (energy được cập nhật tự động)
export async function GET(request: NextRequest) {
    try {
        const { searchParams } = new URL(request.url);
        const userIdParam = searchParams.get("userId");

        if (!userIdParam) {
            return NextResponse.json({ error: "Missing userId parameter" }, { status: 400 });
        }

        const userId = parseInt(userIdParam);
        if (isNaN(userId)) {
            return NextResponse.json({ error: "Invalid userId format" }, { status: 400 });
        }

        const user = await getUserWithUpdatedEnergy(userId);
        if (!user) {
            return NextResponse.json({ error: "User not found" }, { status: 404 });
        }

        return NextResponse.json({
            success: true,
            user
        });
    } catch (error) {
        console.error("Error in GET game actions API:", error);
        return NextResponse.json({ error: "Internal server error" }, { status: 500 });
    }
}
