// src/components/game/LocationManager.tsx
"use client";

import {
    isLocationManagerSupported,
    locationManager,
    mountLocationManager,
    unmountLocationManager
} from "@telegram-apps/sdk";
import { useTranslations } from "next-intl";
import { useEffect, useRef, useState } from "react";

import { useGameStore } from "@/store/gameStore";
import { GamePosition } from "@/types/game";

interface LocationManagerProps {
    onLocationGranted: (position: GamePosition) => void;
    onLocationDenied: () => void;
}

export function LocationManager({ onLocationGranted, onLocationDenied }: LocationManagerProps) {
    const t = useTranslations("location");
    const [locationState, setLocationState] = useState<"initial" | "requesting" | "granted" | "denied">("initial");
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const isMountingRef = useRef(false);
    const isMountedRef = useRef(false);
    const { userPosition } = useGameStore();

    // Mount location manager khi component load
    useEffect(() => {
        const initLocationManager = async () => {
            // Prevent concurrent mounting
            if (isMountingRef.current || isMountedRef.current) {
                return;
            }

            if (!isLocationManagerSupported()) {
                return;
            }

            if (!mountLocationManager.isAvailable()) {
                return;
            }

            try {
                isMountingRef.current = true;

                await mountLocationManager();

                isMountedRef.current = true;
                isMountingRef.current = false;
            } catch (err) {
                isMountingRef.current = false;

                // Only show error if it's not a concurrent call error
                if (err instanceof Error && !err.message.includes("already mounting")) {
                    setError(t("locationUnavailable"));
                }
            }
        };

        initLocationManager();

        // Cleanup on unmount
        return () => {
            if (isMountedRef.current) {
                try {
                    unmountLocationManager();
                    isMountedRef.current = false;
                    isMountingRef.current = false;
                } catch (err) {
                    // Silent error handling
                }
            }
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // Don't render if location is already granted and stored in game store
    if (userPosition) {
        return null;
    }

    // Don't render if location is already granted (this check is at the end for safety)
    if (locationState === "granted" && userPosition) {
        return null;
    }

    const handleEnableLocation = async () => {
        // Prevent multiple simultaneous requests
        if (isLoading) {
            return;
        }

        setIsLoading(true);
        setLocationState("requesting");
        setError(null);

        try {
            // Check if location request is available
            if (!locationManager.requestLocation?.isAvailable()) {
                // Fallback to browser geolocation
                if (!navigator.geolocation) {
                    throw new Error(t("geolocationNotSupported"));
                }

                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        const userPos: GamePosition = {
                            lat: position.coords.latitude,
                            lng: position.coords.longitude
                        };
                        setLocationState("granted");
                        onLocationGranted(userPos);
                        setIsLoading(false);
                    },
                    (error) => {
                        setError(t("failedToGetLocation"));
                        setLocationState("denied");
                        onLocationDenied();
                        setIsLoading(false);
                    }
                );
                return;
            }

            // Use Telegram location manager
            const location = await locationManager.requestLocation();

            console.log("Telegram location received:", location);
            const userPos: GamePosition = {
                lat: location.latitude,
                lng: location.longitude
            };

            setLocationState("granted");
            onLocationGranted(userPos);
        } catch (err) {
            console.error("Location request failed:", err);
            setError(err instanceof Error ? err.message : "Failed to get location");
            setLocationState("denied");
            onLocationDenied();
        } finally {
            setIsLoading(false);
        }
    };

    const openLocationSettings = () => {
        if (locationManager.openSettings?.isAvailable()) {
            locationManager.openSettings();
        } else {
            // Fallback: show instructions
            alert(t("enableLocationInstructions"));
        }
    };

    return (
        <div className="absolute inset-0 bg-gradient-to-br from-purple-600 via-blue-600 to-indigo-700 flex flex-col justify-center items-center text-white z-40">
            {/* Main content based on state */}
            {locationState === "initial" && (
                <div className="text-center px-6 max-w-sm">
                    {/* Map preview placeholder */}
                    <div className="mb-8 relative">
                        <div className="w-64 h-40 bg-white bg-opacity-20 rounded-2xl mx-auto p-4 backdrop-blur-sm">
                            {/* Grid pattern */}
                            <div className="grid grid-cols-6 gap-1 h-full opacity-30">
                                {Array.from({ length: 18 }, (_, i) => (
                                    <div key={i} className="bg-white rounded-sm"></div>
                                ))}
                            </div>

                            {/* Floating treasures */}
                            <div className="absolute inset-0 flex items-center justify-center">
                                <div className="absolute top-4 left-8 text-2xl animate-bounce">⭐</div>
                                <div
                                    className="absolute top-8 right-6 text-2xl animate-bounce"
                                    style={{ animationDelay: "0.5s" }}>
                                    🏆
                                </div>
                                <div
                                    className="absolute bottom-6 left-12 text-2xl animate-bounce"
                                    style={{ animationDelay: "1s" }}>
                                    💰
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Text content */}
                    <h2 className="text-2xl font-bold mb-3">{t("enableLocationTitle")}</h2>
                    <p className="text-white text-opacity-80 mb-8">{t("enableLocationSubtitle")}</p>

                    {/* Enable button */}
                    <button
                        onClick={handleEnableLocation}
                        disabled={isLoading}
                        className="w-full bg-white hover:bg-gray-100 text-black font-bold py-4 px-6 rounded-2xl shadow-lg transition-all duration-300 hover:scale-105 disabled:opacity-70 disabled:cursor-not-allowed flex items-center justify-center gap-3">
                        <span className="text-red-500 text-xl">📍</span>
                        <span>{t("enableLocation")}</span>
                    </button>
                </div>
            )}

            {locationState === "requesting" && isLoading && (
                <div className="text-center px-6">
                    <div className="relative mb-6">
                        <div className="w-16 h-16 border-4 border-white border-opacity-20 rounded-full mx-auto"></div>
                        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-16 h-16 border-4 border-white border-t-transparent rounded-full animate-spin"></div>
                    </div>
                    <h3 className="text-xl font-bold mb-2">{t("requestingLocation")}</h3>
                    <p className="text-white text-opacity-80">{t("requestingLocationSubtitle")}</p>
                </div>
            )}

            {locationState === "denied" && (
                <div className="text-center px-6 max-w-sm">
                    <div className="text-6xl mb-6">📍</div>
                    <h3 className="text-xl font-bold mb-3">{t("locationAccessNeeded")}</h3>
                    <p className="text-white text-opacity-80 mb-8">{t("locationAccessDescription")}</p>
                    <button
                        onClick={openLocationSettings}
                        className="w-full bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-6 py-3 rounded-full font-semibold backdrop-blur-sm transition-all">
                        {t("openSettings")}
                    </button>
                </div>
            )}

            {/* Error message */}
            {error && (
                <div className="absolute bottom-20 left-4 right-4">
                    <div className="bg-red-500 bg-opacity-20 border border-red-400 text-white p-3 rounded-lg backdrop-blur-sm">
                        <div className="flex items-center justify-between">
                            <span className="text-sm">{error}</span>
                            <button
                                onClick={handleEnableLocation}
                                className="bg-white bg-opacity-20 hover:bg-opacity-30 px-3 py-1 rounded text-xs font-semibold transition-all">
                                {t("retry")}
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}
