/* src/components/Root/styles.css */

/* Reset và setup c<PERSON> bản */
* {
    box-sizing: border-box;
}

html, body {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

/* <PERSON><PERSON><PERSON> bảo Next.js container chiếm full height */
#__next {
    width: 100%;
    height: 100%;
}

/* Root container */
.root-inner {
    width: 100%;
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height cho mobile */
    overflow: hidden;
    position: relative;
}

/* App container */
.app-container {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;
    -touch-action: manipulation;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -webkit-tap-highlight-color: transparent;
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
}

/* Content có thể scroll */
.content-scrollable {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
}

/* Loading state */
.root__loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100vh;
    width: 100%;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    color: var(--tg-theme-text-color, #000);
    background-color: var(--tg-theme-bg-color, #fff);
}

.root__loading-spinner {
    font-size: 16px;
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
}

/* Fix cho iOS Safari */
.ios-fix {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

/* Telegram Web App specific với CSS variables */
.tg-viewport {
    width: 100%;
    height: var(--tg-viewport-height, 100vh);
    overflow: hidden;
}

/* Đảm bảo không có margin/padding không mong muốn */
.tgui-d88eee1798de4e11 {
    height: 100%;
}

/* Override Telegram UI default styles nếu cần */
.tgui-root {
    height: 100%;
}

/* Prevent bounce scrolling on iOS */
body {
    position: fixed;
    width: 100%;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior-y: none;
}

/* Theme-aware scrollbar */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: var(--tg-theme-secondary-bg-color, #f1f1f1);
}

::-webkit-scrollbar-thumb {
    background: var(--tg-theme-hint-color, #888);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--tg-theme-text-color, #555);
}