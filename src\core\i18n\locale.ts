// src/core/i18n/locale.ts
"use server";

import { cookies } from "next/headers";

import { defaultLocale, mapTelegramLanguageToLocale } from "./config";

import type { Locale } from "./types";

// In this example the locale is read from a cookie. You could alternatively
// also read it from a database, backend service, or any other source.
const COOKIE_NAME = "NEXT_LOCALE";
const TELEGRAM_LOCALE_COOKIE = "TELEGRAM_DETECTED_LOCALE";

const getLocale = async (): Promise<string> => {
    const cookieStore = await cookies();
    
    // First, try to get manually set locale
    const manualLocale = cookieStore.get(COOKIE_NAME)?.value;
    if (manualLocale) {
        return manualLocale;
    }
    
    // Then, try to get Telegram detected locale
    const telegramLocale = cookieStore.get(TELEGRAM_LOCALE_COOKIE)?.value;
    if (telegramLocale) {
        return telegramLocale;
    }
    
    // Fallback to default
    return defaultLocale;
};

const setLocale = async (locale?: string) => {
    const cookieStore = await cookies();
    const targetLocale = (locale as Locale) || defaultLocale;
    
    cookieStore.set(COOKIE_NAME, targetLocale, {
        maxAge: 60 * 60 * 24 * 365, // 1 year
        path: '/',
        sameSite: 'strict'
    });
};

/**
 * Set locale based on Telegram user language
 * This should be called when Telegram initData is available
 */
const setTelegramLocale = async (telegramLanguageCode?: string) => {
    const cookieStore = await cookies();
    const detectedLocale = mapTelegramLanguageToLocale(telegramLanguageCode);
    
    // Only set if no manual locale is already set
    const existingManualLocale = cookieStore.get(COOKIE_NAME)?.value;
    if (!existingManualLocale) {
        cookieStore.set(TELEGRAM_LOCALE_COOKIE, detectedLocale, {
            maxAge: 60 * 60 * 24 * 30, // 30 days
            path: '/',
            sameSite: 'strict'
        });
        
        console.log(`Auto-detected locale from Telegram: ${telegramLanguageCode} -> ${detectedLocale}`);
    }
};

/**
 * Clear Telegram locale detection (useful for testing or manual override)
 */
const clearTelegramLocale = async () => {
    const cookieStore = await cookies();
    cookieStore.delete(TELEGRAM_LOCALE_COOKIE);
};

/**
 * Get current locale source for debugging
 */
const getLocaleSource = async (): Promise<{
    current: string;
    source: 'manual' | 'telegram' | 'default';
    telegramDetected?: string;
}> => {
    const cookieStore = await cookies();
    
    const manualLocale = cookieStore.get(COOKIE_NAME)?.value;
    const telegramLocale = cookieStore.get(TELEGRAM_LOCALE_COOKIE)?.value;
    
    if (manualLocale) {
        return {
            current: manualLocale,
            source: 'manual',
            telegramDetected: telegramLocale
        };
    }
    
    if (telegramLocale) {
        return {
            current: telegramLocale,
            source: 'telegram'
        };
    }
    
    return {
        current: defaultLocale,
        source: 'default'
    };
};

export { 
    getLocale, 
    setLocale, 
    setTelegramLocale, 
    clearTelegramLocale, 
    getLocaleSource 
};