"use client";

import { useGameMechanics } from "@/hooks/useGameActions";
import { useUserSync } from "@/hooks/useUserSync";
import { useGameStore } from "@/store/gameStore";
import { useState } from "react";

const TasksSection = () => {
    const { collectedEggs } = useGameStore();
    const { user, refreshUser } = useUserSync();
    const { loading, error, performGameAction, clearError } = useGameMechanics(user?.id || null);
    const [actionResult, setActionResult] = useState<any>(null);

    // Demo tasks with different rewards
    const tasks = [
        {
            id: "treasure_hunt",
            nameKey: "Complete 3 treasure hunts",
            current: Math.min(collectedEggs, 3),
            target: 3,
            energyCost: 1,
            expReward: 10,
            tokenReward: 5,
            completed: collectedEggs >= 3
        },
        {
            id: "daily_tap",
            nameKey: "Collect 10 tokens",
            current: 10, // Demo value
            target: 10,
            energyCost: 0,
            expReward: 5,
            tokenReward: 2,
            completed: true
        },
        {
            id: "share_friend",
            nameKey: "Share with 1 friend",
            current: 0,
            target: 1,
            energyCost: 0,
            expReward: 15,
            tokenReward: 10,
            completed: false
        }
    ];

    const handleTaskAction = async (task: (typeof tasks)[0]) => {
        if (!user) {
            alert("Please wait for user data to load");
            return;
        }

        if (task.completed) {
            alert("Task already completed!");
            return;
        }

        if (task.energyCost > 0 && task.energyCost > user.energy) {
            alert(`Not enough energy! Need ${task.energyCost}, have ${user.energy}`);
            return;
        }

        clearError();
        setActionResult(null);

        try {
            const result = await performGameAction({
                energyCost: task.energyCost,
                expReward: task.expReward,
                tokenReward: task.tokenReward
            });

            setActionResult({
                taskName: task.nameKey,
                ...result
            });

            // Refresh user data to update UI
            setTimeout(() => {
                refreshUser();
            }, 500);
        } catch (error) {
            console.error("Task action failed:", error);
        }
    };

    return (
        <div className="w-full h-full p-6 overflow-y-auto">
            <h2 className="text-2xl font-bold mb-6 text-gray-800">Your Tasks</h2>

            {/* Error display */}
            {error && (
                <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-red-600 text-sm">{error}</p>
                    <button onClick={clearError} className="mt-2 text-red-500 text-xs underline">
                        Close
                    </button>
                </div>
            )}

            {/* Action result */}
            {actionResult && (
                <div
                    className={`mb-4 p-3 rounded-lg border ${
                        actionResult.success ? "bg-green-50 border-green-200" : "bg-red-50 border-red-200"
                    }`}>
                    <h4 className="font-semibold text-sm mb-1">Result: {actionResult.taskName}</h4>
                    {actionResult.success ? (
                        <div className="text-green-700 text-sm space-y-1">
                            {actionResult.results.energy && (
                                <p>⚡ Energy remaining: {actionResult.results.energy.current_energy}</p>
                            )}
                            {actionResult.results.experience && (
                                <p>
                                    📈 Level {actionResult.results.experience.new_level}, Progress{" "}
                                    {actionResult.results.experience.new_progress}/100
                                    {actionResult.results.experience.level_up && (
                                        <span className="ml-2 px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
                                            LEVEL UP! 🎉
                                        </span>
                                    )}
                                </p>
                            )}
                            {actionResult.results.balance && <p>💰 HUFI: {actionResult.results.balance.new_balance}</p>}
                        </div>
                    ) : (
                        <p className="text-red-700 text-sm">❌ {actionResult.error}</p>
                    )}
                </div>
            )}

            <div className="space-y-4">
                <div className="bg-white rounded-xl p-6 shadow-md border border-gray-100">
                    <div className="flex items-center justify-between mb-3">
                        <h3 className="font-bold text-lg text-gray-800">Daily Tasks</h3>
                        <span className="text-sm bg-purple-100 text-purple-600 px-2 py-1 rounded-full">Daily</span>
                    </div>

                    <div className="space-y-4">
                        {tasks.map((task) => (
                            <div key={task.id}>
                                <div className="flex justify-between text-sm mb-2">
                                    <span>{task.nameKey}</span>
                                    <span className="font-medium">
                                        {task.current}/{task.target}
                                    </span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                                    <div
                                        className={`h-2 rounded-full transition-all duration-300 ${
                                            task.completed ? "bg-green-600" : "bg-purple-600"
                                        }`}
                                        style={{
                                            width: `${Math.min((task.current / task.target) * 100, 100)}%`
                                        }}
                                    />
                                </div>
                                <div className="flex justify-between items-center">
                                    <div className="text-xs text-gray-600">
                                        {task.completed ? "✓ Completed!" : `${task.target - task.current} more to go`}
                                    </div>
                                    {task.energyCost > 0 && (
                                        <button
                                            onClick={() => handleTaskAction(task)}
                                            disabled={
                                                loading || task.completed || (user?.energy || 0) < task.energyCost
                                            }
                                            className={`px-3 py-1 rounded-lg text-xs font-medium transition-colors ${
                                                task.completed
                                                    ? "bg-green-100 text-green-600 cursor-not-allowed"
                                                    : (user?.energy || 0) >= task.energyCost
                                                      ? "bg-purple-100 text-purple-600 hover:bg-purple-200"
                                                      : "bg-gray-100 text-gray-400 cursor-not-allowed"
                                            }`}>
                                            {task.completed
                                                ? "✓ Done"
                                                : loading
                                                  ? "Processing..."
                                                  : `⚡${task.energyCost} 💰${task.tokenReward}`}
                                        </button>
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                <div className="bg-white rounded-xl p-6 shadow-md border border-gray-100">
                    <div className="flex items-center justify-between mb-3">
                        <h3 className="font-bold text-lg text-gray-800">Weekly Challenges</h3>
                        <span className="text-sm bg-blue-100 text-blue-600 px-2 py-1 rounded-full">Weekly</span>
                    </div>

                    <div className="space-y-4">
                        <div>
                            <div className="flex justify-between text-sm mb-2">
                                <span>Collect 50 treasures</span>
                                <span className="font-medium">{Math.min(collectedEggs, 50)}/50</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div
                                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                    style={{
                                        width: `${Math.min((collectedEggs / 50) * 100, 100)}%`
                                    }}></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default TasksSection;
