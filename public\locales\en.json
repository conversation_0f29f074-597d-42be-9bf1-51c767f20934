{"app": {"title": "HuntFi", "subtitle": "Explore. Hunt. Earn.", "description": "Discover treasures around you. Earn while you explore.", "fullDescription": "HuntFi – Explore the map, unlock treasure chests near you, and earn unique crypto rewards. A real-world GameFi x DeFi adventure!"}, "location": {"enableLocation": "Enable Location", "enableLocationTitle": "Enable location and start your crypto treasure hunt!", "enableLocationSubtitle": "Discover hidden crypto treasures and earn tokens around you", "requestingLocation": "Requesting location...", "requestingLocationSubtitle": "Please allow location access when prompted", "locationAccessNeeded": "Location Access Needed", "locationAccessDescription": "Please grant location access to start your GameFi adventure!", "openSettings": "Open Settings", "retry": "Retry", "locationUnavailable": "Location service not available", "geolocationNotSupported": "Geolocation is not supported", "failedToGetLocation": "Failed to get location", "enableLocationInstructions": "Please enable location access in your device settings and refresh the app."}, "game": {"startHunting": "Start Hunting", "stopHunt": "Stop Hunt", "foundTreasure": "Found Treasure", "huntTimer": "<PERSON>r", "energy": "Energy", "tokens": "Tokens", "level": "Level", "treasuresFound": "Treasures Found", "huntingActive": "Hunting Active", "collectReward": "Collect Reward", "openChest": "Open Chest", "continueHunting": "Continue Hunting", "congratulations": "Congratulations!", "youEarned": "You earned", "cryptoRewards": "Crypto Rewards", "defiStaking": "<PERSON><PERSON>i <PERSON>", "gamefiTokens": "<PERSON><PERSON><PERSON>"}, "navigation": {"hunt": "<PERSON>", "tasks": "Tasks", "league": "League", "refer": "<PERSON><PERSON>", "profile": "Profile"}, "tasks": {"dailyTasks": "Daily Tasks", "completedTasks": "Completed Tasks", "earnMoreTokens": "<PERSON><PERSON><PERSON>", "completeHunts": "Complete treasure hunts", "referFriends": "Refer friends to HuntFi", "stakingRewards": "Claim staking rewards"}, "leaderboard": {"title": "Leaderboard", "topHunters": "Top Hunters", "yourRank": "Your Rank", "weeklyLeaders": "Weekly Leaders", "allTimeLeaders": "All-Time Leaders"}, "referral": {"title": "Refer Friends", "description": "Invite friends to HuntFi and earn bonus crypto rewards when they join!", "shareInvite": "Share Invite Link", "yourReferrals": "Your Referrals", "bonusEarned": "Bonus Earned", "inviteRewards": "Invite & Earn <PERSON>o"}, "profile": {"title": "Your Profile", "totalTokens": "Total Tokens", "treasuresCollected": "Treasures Collected", "referrals": "Referrals", "level": "Level", "cryptoWallet": "Crypto Wallet", "stakingPool": "Staking Pool", "defiBalance": "<PERSON><PERSON><PERSON>"}, "rewards": {"bronzeChest": "Bronze Chest", "silverChest": "Silver Chest", "goldChest": "Gold Chest", "legendaryChest": "Legendary Chest", "cryptoBonus": "Crypto Bonus", "stakingBonus": "Staking Bonus", "defiReward": "<PERSON><PERSON><PERSON>"}, "i18n": {"header": "HuntFi supports multiple languages", "footer": "You can select a different language from the dropdown menu."}, "loading": {"title": "HuntFi", "subtitle": "GameFi x DeFi Adventure", "progress": "Loading progress", "hint": "Preparing for your crypto treasure hunt...", "messages": {"starting": "Starting HuntFi...", "loadingMap": "Loading treasure map...", "preparingTreasures": "Preparing crypto treasures...", "settingLocation": "Setting up location...", "preparingAdventure": "Preparing for GameFi adventure...", "connectingDefi": "Connecting to DeFi protocols..."}}}