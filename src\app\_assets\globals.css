@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

:root {
  --primary: #6C63FF;
  --secondary: #FF6584;
  --safe-area-inset-top: env(safe-area-inset-top, 0px);
  --safe-area-inset-bottom: env(safe-area-inset-bottom, 0px);
  --safe-area-inset-left: env(safe-area-inset-left, 0px);
  --safe-area-inset-right: env(safe-area-inset-right, 0px);
}

* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  /* Prevent viewport issues */
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

/* Remove default margins and ensure full height */
#__next, [data-reactroot] {
  min-height: 100vh;
  height: 100%;
  width: 100%;
}

body {
  background: var(--tg-theme-secondary-bg-color, linear-gradient(135deg, #f5f7fa 0%, #e4edf5 100%));
}

/* Progress bar styles */
.progress-bar {
  height: 12px;
  border-radius: 20px;
  overflow: hidden;
  background-color: #e0e0e0;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--secondary), var(--primary));
  border-radius: 20px;
  transition: width 0.5s ease;
}

/* User marker animation */
.user-marker {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: var(--primary);
  border: 3px solid white;
  box-shadow: 0 0 0 3px var(--primary);
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { 
    box-shadow: 0 0 0 0 rgba(108, 99, 255, 0.7); 
  }
  70% { 
    box-shadow: 0 0 0 10px rgba(108, 99, 255, 0); 
  }
  100% { 
    box-shadow: 0 0 0 0 rgba(108, 99, 255, 0); 
  }
}

/* Navigation container styles */
.nav-container {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.1);
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
}

.nav-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 12px;
  border-radius: 20px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  user-select: none;
}

.nav-icon {
  width: 48px;
  height: 48px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(108, 99, 255, 0.08);
  border: 2px solid transparent;
}

.nav-icon i {
  font-size: 20px;
  color: #9CA3AF;
  transition: all 0.3s ease;
}

.nav-label {
  font-size: 11px;
  font-weight: 500;
  color: #9CA3AF;
  margin-top: 4px;
  transition: all 0.3s ease;
  opacity: 0.8;
}

.nav-item.active .nav-icon {
  background: linear-gradient(135deg, #6C63FF 0%, #5A52E3 100%);
  border-color: rgba(108, 99, 255, 0.3);
  box-shadow: 0 8px 25px rgba(108, 99, 255, 0.3);
  transform: scale(1.05);
}

.nav-item.active .nav-icon i {
  color: white;
  transform: scale(1.1);
}

.nav-item.active .nav-label {
  color: #6C63FF;
  font-weight: 600;
  opacity: 1;
}

.nav-center {
  position: relative;
  margin-top: -20px;
}

.nav-center .nav-icon {
  width: 64px;
  height: 64px;
  border-radius: 20px;
  background: linear-gradient(135deg, #6C63FF 0%, #5A52E3 100%);
  box-shadow: 0 12px 30px rgba(108, 99, 255, 0.4);
  border: 4px solid white;
}

.nav-center .nav-icon i {
  color: white;
  font-size: 24px;
}

.nav-center .nav-label {
  color: #6C63FF;
  font-weight: 600;
  margin-top: 8px;
}

/* Modal animations */
.modal-enter {
  animation: modalEnter 0.3s forwards;
}

@keyframes modalEnter {
  from { 
    transform: scale(0.8); 
    opacity: 0; 
  }
  to { 
    transform: scale(1); 
    opacity: 1; 
  }
}

/* Token count animation */
.token-count {
  animation: pop 0.3s ease;
}

@keyframes pop {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

/* Collection animation */
.collect-animation {
  animation: collect 0.8s forwards;
}

@keyframes collect {
  0% { 
    transform: scale(1); 
    opacity: 1; 
  }
  100% { 
    transform: scale(3); 
    opacity: 0; 
  }
}

/* Leaflet map styles */
.leaflet-container {
  height: 100%;
  width: 100%;
  border-radius: 0;
}

.leaflet-control-container {
  display: none;
}

.user-marker-container {
  border: none !important;
  background: transparent !important;
}

.egg-marker-container {
  border: none !important;
  background: transparent !important;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #6C63FF 0%, #5A52E3 100%);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5A52E3 0%, #4A46C7 100%);
}

/* Responsive design */
@media (max-width: 640px) {
  .nav-icon {
    width: 40px;
    height: 40px;
  }
  
  .nav-center .nav-icon {
    width: 56px;
    height: 56px;
  }
  
  .nav-label {
    font-size: 10px;
  }
}

/* Loading spinner */
@keyframes spin {
  to { transform: rotate(360deg); }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Bounce animation */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

.animate-bounce {
  animation: bounce 1s ease-in-out infinite;
}

/* Button hover effects */
button:active {
  transform: scale(0.95);
}

/* Disable text selection on game elements */
.nav-item, .nav-icon, .nav-label {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
/* Telegram specific overrides */
.root-inner {
  min-height: 100vh !important;
  height: 100% !important;
  width: 100% !important;
}

/* App container takes full space */
.app-container {
  min-height: 100vh !important;
  height: 100% !important;
  width: 100% !important;
  position: relative;
}

/* Loading screen full coverage */
.root__loading {
  position: fixed !important;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
}

/* Ensure no scrollbars appear */
.tgui-root {
  overflow: hidden !important;
  height: 100% !important;
  width: 100% !important;
}

/* Remove any potential margins from Telegram UI components */
.tgui-root * {
  box-sizing: border-box;
}

/* Force fullscreen for mobile viewports */
@supports (-webkit-appearance: none) {
  html, body {
    height: -webkit-fill-available;
  }

  .app-container {
    min-height: -webkit-fill-available;
  }
}

/* Avatar cutout mask for ProfileSection - more reliable than inline styles */
.avatar-cutout-mask {
  /* Webkit prefixes for Safari and older Chrome */
  -webkit-mask-image: radial-gradient(circle 50px at 50% 25%, transparent 39px, black 41px);
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center top;
  -webkit-mask-size: 100% 100%;

  /* Standard properties */
  mask-image: radial-gradient(circle 50px at 50% 25%, transparent 39px, black 41px);
  mask-repeat: no-repeat;
  mask-position: center top;
  mask-size: 100% 100%;

  /* Force hardware acceleration for better performance */
  transform: translateZ(0);
  will-change: mask-image;
}

/* Ensure mask is applied after page load */
.avatar-cutout-mask.loaded {
  animation: maskFadeIn 0.1s ease-in-out;
}

@keyframes maskFadeIn {
  from { opacity: 0.99; }
  to { opacity: 1; }
}

/* Fallback for browsers that don't support mask-image */
@supports not (mask-image: radial-gradient(circle, transparent, black)) and not (-webkit-mask-image: radial-gradient(circle, transparent, black)) {
  .avatar-cutout-mask {
    /* Use clip-path as fallback - creates a notch effect */
    clip-path: polygon(0% 0%, 0% 100%, 45% 100%, 45% 30%, 55% 30%, 55% 100%, 100% 100%, 100% 0%);
  }
}

/* Prevent zoom on input focus (mobile) */
input, textarea, select {
  font-size: 16px !important;
  transform-origin: left top;
  transform: scale(1);
}

/* Hide any potential scrollbars */
::-webkit-scrollbar {
  display: none;
}

* {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Fullscreen game styles */
.game-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
  background: #000;
}

/* Map container full size */
.leaflet-container {
  width: 100% !important;
  height: 100% !important;
}
/* Header với safe area padding */
.game-header {
  padding-top: max(var(--safe-area-inset-top), 20px) !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 1000 !important;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
}

/* Game header user info */
.game-header .user-info {
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 60px;
}

/* Header buttons safe spacing */
.game-header .header-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
  padding-top: 4px;
}

.game-header .close-button,
.game-header .menu-button {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  padding: 8px 12px !important;
  border-radius: 12px !important;
  font-weight: 500 !important;
  backdrop-filter: blur(5px) !important;
  min-height: 36px !important;
  min-width: 60px !important;
}

/* Map container với header offset */
.leaflet-container {
  padding-top: calc(var(--safe-area-inset-top) + 80px) !important;
  height: 100vh !important;
  width: 100vw !important;
}

/* Bottom navigation safe area */
.bottom-navigation {
  padding-bottom: max(var(--safe-area-inset-bottom), 20px) !important;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
}

/* Game UI với proper spacing */
.game-ui-overlay {
  padding-top: calc(var(--safe-area-inset-top) + 100px);
  padding-bottom: calc(var(--safe-area-inset-bottom) + 100px);
  padding-left: max(var(--safe-area-inset-left), 16px);
  padding-right: max(var(--safe-area-inset-right), 16px);
}

/* iPhone X và các models mới */
@supports (padding: max(0px)) {
  .game-header {
    padding-top: max(44px, var(--safe-area-inset-top)) !important;
  }
  
  .leaflet-container {
    padding-top: max(124px, calc(var(--safe-area-inset-top) + 80px)) !important;
  }
}

/* Android với notch */
@media screen and (max-width: 414px) {
  .game-header {
    padding-top: max(var(--safe-area-inset-top), 30px) !important;
  }
  
  .game-header .user-info {
    min-height: 50px;
    padding: 6px 12px;
  }
  
  .leaflet-container {
    padding-top: calc(var(--safe-area-inset-top) + 90px) !important;
  }
}

/* Landscape mode adjustments */
@media screen and (orientation: landscape) {
  .game-header {
    padding-top: max(var(--safe-area-inset-top), 10px) !important;
  }
  
  .game-header .user-info {
    min-height: 40px;
    padding: 4px 16px;
  }
  
  .leaflet-container {
    padding-top: calc(var(--safe-area-inset-top) + 60px) !important;
  }
}

/* Fallback cho browsers không support env() */
@supports not (padding: env(safe-area-inset-top)) {
  .game-header {
    padding-top: 44px !important; /* iOS status bar height */
  }
  
  .leaflet-container {
    padding-top: 124px !important;
  }
  
  .bottom-navigation {
    padding-bottom: 34px !important; /* iOS home indicator */
  }
}