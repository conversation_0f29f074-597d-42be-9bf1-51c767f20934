// src/types/game.ts
export interface EggType {
    type: "bronze" | "silver" | "gold";
    color: string;
    tokenValue: number;
}

export interface GamePosition {
    lat: number;
    lng: number;
}

export interface GameEgg {
    id: string;
    position: GamePosition;
    eggType: EggType;
    collected: boolean;
}

// Supabase user data structure
export interface DatabaseUserData {
    balance: number;
    level: number;
    level_progress: number;
    energy: number;
    energy_total: number;
}

export interface GameState {
    tokens: number;
    level: number;
    xp: number;
    nextLevelXp: number;
    isPlaying: boolean;
    collectedEggs: number;
    eggs: GameEgg[];
    energy: number;
    maxEnergy: number;
    huntTimer: NodeJS.Timeout | null;
    energyTimer: NodeJS.Timeout | null;
    huntEndTime: number | null;
    userPosition: GamePosition | null;
}

export interface GameActions {
    // User data sync with Supabase
    syncUserData: (userData: DatabaseUserData) => void;
    
    // Position and eggs
    setUserPosition: (position: GamePosition) => void;
    spawnEggs: () => void;
    
    // Hunt actions
    startHunt: () => void;
    endHunt: () => void;
    updateHuntTimer: () => void;
    
    // Egg collection
    collectEgg: (eggId: string) => void;
    
    // Local state updates (for immediate UI feedback)
    addTokens: (amount: number) => void;
    addXP: (amount: number) => void;
    rechargeEnergy: () => void;
    
    // External updates from Supabase
    updateEnergy: (newEnergy: number) => void;
    updateTokens: (newTokens: number) => void;
    updateLevel: (newLevel: number, newProgress: number) => void;
}

export type GameStore = GameState & GameActions;