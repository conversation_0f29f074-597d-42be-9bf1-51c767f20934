<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Egg Hunt - Token Collection Game</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&display=swap');
        
        :root {
            --primary: #6C63FF;
            --secondary: #FF6584;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #e4edf5 100%);
            height: 100vh;
            overflow: hidden;
        }
        
        .progress-bar {
            height: 12px;
            border-radius: 20px;
            overflow: hidden;
            background-color: #e0e0e0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--secondary), var(--primary));
            border-radius: 20px;
            transition: width 0.5s ease;
        }
        
        .user-marker {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: var(--primary);
            border: 3px solid white;
            box-shadow: 0 0 0 3px var(--primary);
            animation: pulse 1.5s infinite;
        }
        
        .nav-container {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.1);
            border-top-left-radius: 20px;
            border-top-right-radius: 20px;
        }
        
        .nav-item {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 12px;
            border-radius: 20px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            user-select: none;
        }
        
        .nav-icon {
            width: 48px;
            height: 48px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(108, 99, 255, 0.08);
            border: 2px solid transparent;
        }
        
        .nav-icon i {
            font-size: 20px;
            color: #9CA3AF;
            transition: all 0.3s ease;
        }
        
        .nav-label {
            font-size: 11px;
            font-weight: 500;
            color: #9CA3AF;
            margin-top: 4px;
            transition: all 0.3s ease;
            opacity: 0.8;
        }
        
        .nav-item.active .nav-icon {
            background: linear-gradient(135deg, #6C63FF 0%, #5A52E3 100%);
            border-color: rgba(108, 99, 255, 0.3);
            box-shadow: 0 8px 25px rgba(108, 99, 255, 0.3);
            transform: scale(1.05);
        }
        
        .nav-item.active .nav-icon i {
            color: white;
            transform: scale(1.1);
        }
        
        .nav-item.active .nav-label {
            color: #6C63FF;
            font-weight: 600;
            opacity: 1;
        }
        
        .nav-center {
            position: relative;
            margin-top: -20px;
        }
        
        .nav-center .nav-icon {
            width: 64px;
            height: 64px;
            border-radius: 20px;
            background: linear-gradient(135deg, #6C63FF 0%, #5A52E3 100%);
            box-shadow: 0 12px 30px rgba(108, 99, 255, 0.4);
            border: 4px solid white;
        }
        
        .nav-center .nav-icon i {
            color: white;
            font-size: 24px;
        }
        
        .nav-center .nav-label {
            color: #6C63FF;
            font-weight: 600;
            margin-top: 8px;
        }
        
        .modal-enter {
            animation: modalEnter 0.3s forwards;
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(108, 99, 255, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(108, 99, 255, 0); }
            100% { box-shadow: 0 0 0 0 rgba(108, 99, 255, 0); }
        }
        
        @keyframes modalEnter {
            from { transform: scale(0.8); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
        }
        
        .token-count {
            animation: pop 0.3s ease;
        }
        
        @keyframes pop {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }
        
        .collect-animation {
            animation: collect 0.8s forwards;
        }
        
        @keyframes collect {
            0% { transform: scale(1); opacity: 1; }
            100% { transform: scale(3); opacity: 0; }
        }
    </style>
</head>
<body class="flex flex-col h-screen">
    <!-- Header Section -->
    <header class="bg-white rounded-b-2xl shadow-md py-3 px-4 z-10">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <div class="relative">
                    <div class="w-12 h-12 rounded-full bg-gradient-to-br from-purple-500 to-indigo-600 flex items-center justify-center text-white text-xl font-bold shadow">
                        JD
                    </div>
                    <div class="absolute -bottom-1 left-1/2 transform -translate-x-1/2 bg-yellow-500 text-white text-xs font-bold px-1 rounded-full flex items-center">
                        <i class="fas fa-crown mr-1"></i>
                        Gold
                    </div>
                </div>
                <div>
                    <h1 class="font-bold">John Doe</h1>
                    <div class="flex space-x-3 text-sm text-gray-600">
                        <div class="flex items-center">
                            <i class="fas fa-gem mr-1 text-purple-500"></i>
                            <span>Found 42 Treasure</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-bolt mr-1 text-yellow-500"></i>
                            <span id="energy-count">20/20</span>
                            <span id="energy-timer" class="hidden ml-1 text-xs">(0:00)</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="flex items-center space-x-2">
                <i class="fas fa-coins text-yellow-500 text-lg"></i>
                <span id="token-count" class="font-bold text-xl">1,240</span>
            </div>
        </div>
        
        <div class="mt-3">
            <div class="flex justify-between text-sm mb-1">
                <span class="font-medium">Level 7</span>
                <span class="text-gray-600">240/500 XP</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: 48%"></div>
            </div>
        </div>
    </header>
    
    <!-- Main Content Sections -->
    <main class="flex-1 relative overflow-hidden">
        <!-- Map Section (default visible) -->
        <div id="map-section" class="w-full h-full relative">
            <div id="map" class="w-full h-full z-0"></div>
            
            <!-- Start Button -->
            <button id="start-btn" class="fixed bottom-32 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-purple-500 to-indigo-600 text-white font-bold py-3 px-8 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 z-1">
                <i class="fas fa-play mr-2"></i> Start Hunting
            </button>
        </div>

        <!-- Tasks Section -->
        <div id="tasks-section" class="w-full h-full hidden p-6 overflow-y-auto">
            <h2 class="text-2xl font-bold mb-4">Your Tasks</h2>
            <div class="bg-white rounded-xl p-4 shadow-md mb-4">
                <h3 class="font-bold mb-2">Daily Tasks</h3>
                <p class="text-gray-600">Complete 3 egg hunts</p>
                <div class="mt-2 flex items-center">
                    <div class="w-full bg-gray-200 rounded-full h-2.5">
                        <div class="bg-purple-600 h-2.5 rounded-full" style="width: 66%"></div>
                    </div>
                    <span class="ml-2 text-sm">2/3</span>
                </div>
            </div>
        </div>

        <!-- Leaderboard Section -->
        <div id="leaderboard-section" class="w-full h-full hidden p-6 overflow-y-auto">
            <h2 class="text-2xl font-bold mb-4">Leaderboard</h2>
            <div class="bg-white rounded-xl p-4 shadow-md mb-4">
                <div class="flex items-center mb-3">
                    <div class="w-8 h-8 rounded-full bg-yellow-500 flex items-center justify-center text-white font-bold mr-3">1</div>
                    <div class="flex-1">
                        <h3 class="font-bold">Top Player</h3>
                        <p class="text-sm text-gray-600">5,240 tokens</p>
                    </div>
                    <i class="fas fa-crown text-yellow-500"></i>
                </div>
            </div>
        </div>

        <!-- Refer Section -->
        <div id="refer-section" class="w-full h-full hidden p-6 overflow-y-auto">
            <h2 class="text-2xl font-bold mb-4">Refer Friends</h2>
            <div class="bg-white rounded-xl p-4 shadow-md mb-4 text-center">
                <div class="w-20 h-20 mx-auto bg-purple-100 rounded-full flex items-center justify-center mb-4">
                    <i class="fas fa-user-friends text-3xl text-purple-600"></i>
                </div>
                <p class="mb-4">Invite friends and earn bonus tokens when they join!</p>
                <button class="bg-gradient-to-r from-purple-500 to-indigo-600 text-white font-bold py-3 px-6 rounded-full w-full">
                    Share Invite Link
                </button>
            </div>
        </div>

        <!-- Profile Section -->
        <div id="profile-section" class="w-full h-full hidden p-6 overflow-y-auto">
            <h2 class="text-2xl font-bold mb-4">Your Profile</h2>
            <div class="bg-white rounded-xl p-4 shadow-md mb-4">
                <div class="flex items-center mb-4">
                    <div class="w-16 h-16 rounded-full bg-gradient-to-br from-purple-500 via-purple-600 to-indigo-600 flex items-center justify-center text-white text-2xl font-bold shadow-lg mr-4">
                        JD
                    </div>
                    <div>
                        <h3 class="font-bold">John Doe</h3>
                        <p class="text-sm text-gray-600">Level 7 Hunter</p>
                    </div>
                </div>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span>Total Tokens</span>
                        <span class="font-bold">1,240</span>
                    </div>
                    <div class="flex justify-between">
                        <span>Eggs Collected</span>
                        <span class="font-bold">42</span>
                    </div>
                    <div class="flex justify-between">
                        <span>Referrals</span>
                        <span class="font-bold">3</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Collection Modal -->
        <div id="collection-modal" class="hidden fixed inset-0 flex items-center justify-center z-20">
            <div class="bg-black bg-opacity-50 absolute inset-0"></div>
            <div class="modal-enter bg-white rounded-2xl p-6 max-w-xs w-full mx-4 z-30 text-center">
                <div id="egg-animation" class="w-24 h-24 mx-auto mb-4">
                    <!-- Egg will be inserted here by JS -->
                </div>
                <h3 class="font-bold text-xl mb-2" id="egg-title">You found an egg!</h3>
                <p id="egg-description" class="text-gray-700 mb-4">Open it to reveal your reward!</p>
                <button id="open-egg-btn" class="bg-gradient-to-r from-green-400 to-teal-500 text-white font-bold py-3 px-6 rounded-full w-full">
                    Open Egg
                </button>
            </div>
        </div>

        <!-- Chest Modal -->
        <div id="chest-modal" class="hidden fixed inset-0 flex items-center justify-center z-20">
            <div class="bg-black bg-opacity-50 absolute inset-0"></div>
            <div class="modal-enter bg-white rounded-2xl p-6 max-w-xs w-full mx-4 z-30 text-center">
                <div id="chest-animation" class="w-48 h-48 mx-auto mb-4">
                    <lottie-player 
                        id="chest-lottie"
                        src="https://lottie.host/db1779f8-5fad-4dee-9a73-d225ca1b006e/otIwQVF6ji.json" 
                        background="transparent" 
                        speed="1" 
                        style="width: 100%; height: 100%">
                    </lottie-player>
                </div>
                <h3 class="font-bold text-xl mb-2" id="chest-title">You found a treasure chest!</h3>
                <p id="chest-description" class="text-gray-700 mb-4">Tap to open and collect your reward!</p>
                <button id="open-chest-btn" class="bg-gradient-to-r from-yellow-400 to-yellow-600 text-white font-bold py-3 px-6 rounded-full w-full">
                    Open Chest
                </button>
            </div>
        </div>
        
        <!-- Reward Modal -->
        <div id="reward-modal" class="hidden fixed inset-0 flex items-center justify-center z-20">
            <div class="bg-black bg-opacity-50 absolute inset-0"></div>
            <div class="modal-enter bg-white rounded-2xl p-6 max-w-xs w-full mx-4 z-30 text-center">
                <div class="w-24 h-24 mx-auto mb-4 relative">
                    <div class="absolute inset-0 flex items-center justify-center">
                        <i class="fas fa-gift text-5xl text-yellow-500"></i>
                    </div>
                    <div id="reward-animation" class="w-full h-full">
                        <!-- Animation will be inserted here by JS -->
                    </div>
                </div>
                <h3 class="font-bold text-xl mb-2" id="reward-title">Congratulations!</h3>
                <p id="reward-description" class="text-gray-700 mb-4">You collected <span id="reward-amount" class="font-bold">50</span> tokens!</p>
                <button id="close-reward-btn" class="bg-gradient-to-r from-purple-500 to-indigo-600 text-white font-bold py-3 px-6 rounded-full w-full">
                    Continue Hunting
                </button>
            </div>
        </div>
    </main>
    
    <!-- Bottom Navigation -->
    <nav class="nav-container px-4 py-3">
        <div class="flex justify-around items-end">
            <div class="nav-item" data-section="tasks-section">
                <div class="nav-icon">
                    <i class="fas fa-tasks"></i>
                </div>
                <span class="nav-label">Tasks</span>
            </div>
            
            <div class="nav-item" data-section="leaderboard-section">
                <div class="nav-icon">
                    <i class="fas fa-trophy"></i>
                </div>
                <span class="nav-label">League</span>
            </div>
            
            <div class="nav-item nav-center active" data-section="map-section">
                <div class="nav-icon">
                    <i class="fas fa-running"></i>
                </div>
                <span class="nav-label">Hunt</span>
            </div>
            
            <div class="nav-item" data-section="refer-section">
                <div class="nav-icon">
                    <i class="fas fa-user-friends"></i>
                </div>
                <span class="nav-label">Refer</span>
            </div>
            
            <div class="nav-item" data-section="profile-section">
                <div class="nav-icon">
                    <i class="fas fa-user"></i>
                </div>
                <span class="nav-label">Profile</span>
            </div>
        </div>
    </nav>
    
    <script>
        // Game state
        const gameState = {
            tokens: 1240,
            level: 7,
            xp: 240,
            nextLevelXp: 500,
            isPlaying: false,
            collectedEggs: 42,
            eggs: [],
            energy: 20,
            maxEnergy: 20,
            huntTimer: null,
            energyTimer: null,
            huntEndTime: null
        };
        
        // Egg types
        const eggTypes = [
            { type: 'bronze', color: '#CD7F32', tokenValue: 10 },
            { type: 'silver', color: '#C0C0C0', tokenValue: 25 },
            { type: 'gold', color: '#FFD700', tokenValue: 50 }
        ];
        
        // DOM Elements
        const mapElement = document.getElementById('map');
        const startButton = document.getElementById('start-btn');
        const tokenCountElement = document.getElementById('token-count');
        const collectionModal = document.getElementById('collection-modal');
        const chestModal = document.getElementById('chest-modal');
        const rewardModal = document.getElementById('reward-modal');
        const openEggButton = document.getElementById('open-egg-btn');
        const openChestButton = document.getElementById('open-chest-btn');
        const closeRewardButton = document.getElementById('close-reward-btn');
        const eggAnimation = document.getElementById('egg-animation');
        const chestLottie = document.getElementById('chest-lottie');
        const rewardAnimation = document.getElementById('reward-animation');
        const eggTitle = document.getElementById('egg-title');
        const eggDescription = document.getElementById('egg-description');
        const chestTitle = document.getElementById('chest-title');
        const chestDescription = document.getElementById('chest-description');
        const rewardTitle = document.getElementById('reward-title');
        const rewardDescription = document.getElementById('reward-description');
        const rewardAmount = document.getElementById('reward-amount');
        
        // Map variables
        let map;
        let userMarker;
        let currentPosition = null;
        
        // Initialize the map
        function initMap() {
            const defaultLocation = [37.7749, -122.4194];
            
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    position => {
                        currentPosition = [position.coords.latitude, position.coords.longitude];
                        setupMap(currentPosition);
                    },
                    () => {
                        const message = document.createElement('div');
                        message.className = 'absolute inset-0 flex items-center justify-center z-10 bg-white bg-opacity-90';
                        message.innerHTML = `
                            <div class="text-center p-6 max-w-xs">
                                <i class="fas fa-map-marker-alt text-5xl text-purple-500 mb-4"></i>
                                <h3 class="text-xl font-bold mb-2">Location Required</h3>
                                <p class="mb-4">Turn on location and go find your own treasure!</p>
                                <button onclick="window.location.reload()" class="bg-purple-500 text-white font-bold py-2 px-4 rounded-full">
                                    Try Again
                                </button>
                            </div>
                        `;
                        mapElement.appendChild(message);
                    }
                );
            } else {
                currentPosition = defaultLocation;
                setupMap(currentPosition);
            }
        }
        
        function setupMap(position) {
            map = L.map(mapElement).setView(position, 15);
            
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(map);
            
            const markerElement = document.createElement('div');
            markerElement.className = 'user-marker';
            
            userMarker = L.marker(position, {
                icon: L.divIcon({
                    html: markerElement,
                    className: 'user-marker-container',
                    iconSize: [30, 30]
                }),
                zIndexOffset: 100
            }).addTo(map);
            
            addEggsToMap();
        }
        
        function addEggsToMap() {
            if (!map) return;
            
            gameState.eggs.forEach(egg => map.removeLayer(egg));
            gameState.eggs = [];
            
            for (let i = 0; i < 15; i++) {
                const eggType = eggTypes[Math.floor(Math.random() * eggTypes.length)];
                const offset = 0.005;
                const lat = currentPosition[0] + (Math.random() * offset * 2) - offset;
                const lng = currentPosition[1] + (Math.random() * offset * 2) - offset;
                
                const markerElement = document.createElement('div');
                markerElement.className = 'w-16 h-16';
                markerElement.innerHTML = `
                    <lottie-player 
                        src="https://lottie.host/94a3b444-5feb-49d5-9f7c-ed7c5d5e5759/RSqTPqgz6J.json" 
                        background="transparent" 
                        speed="1" 
                        loop 
                        autoplay
                        style="width: 100%; height: 100%">
                    </lottie-player>
                `;
                
                const marker = L.marker([lat, lng], {
                    icon: L.divIcon({
                        html: markerElement,
                        className: 'egg-marker-container',
                        iconSize: [40, 50]
                    }),
                    zIndexOffset: 10,
                    eggType: eggType
                }).addTo(map);
                
                markerElement.addEventListener('click', () => collectEgg(marker));
                gameState.eggs.push(marker);
            }
        }
        
        function collectEgg(marker) {
            if (!gameState.isPlaying) return;
            
            const eggType = marker.eggType;
            
            if (Math.random() < 0.3) {
                chestLottie.stop();
                chestTitle.textContent = "You found a treasure chest!";
                chestDescription.textContent = "Tap to open and collect your reward!";
                chestModal.classList.remove('hidden');
                
                openChestButton.dataset.marker = JSON.stringify({
                    position: marker.getLatLng(),
                    type: 'chest'
                });
            } else {
                eggAnimation.innerHTML = '';
                const treasureElement = document.createElement('div');
                treasureElement.className = 'text-8xl mx-auto';
                treasureElement.innerHTML = '💰';
                treasureElement.style.filter = eggType.type === 'gold' ? 'drop-shadow(0 5px 10px rgba(255, 215, 0, 0.6))' : 
                                             eggType.type === 'silver' ? 'drop-shadow(0 5px 10px rgba(192, 192, 192, 0.6))' : 
                                             'drop-shadow(0 5px 10px rgba(205, 127, 50, 0.6))';
                eggAnimation.appendChild(treasureElement);
                
                eggTitle.textContent = `You found a ${eggType.type} treasure!`;
                eggDescription.textContent = "Open it to reveal your reward!";
                
                collectionModal.classList.remove('hidden');
                
                openEggButton.dataset.marker = JSON.stringify({
                    position: marker.getLatLng(),
                    type: eggType.type
                });
            }
        }
        
        function openEgg() {
            const markerData = JSON.parse(openEggButton.dataset.marker);
            const eggType = eggTypes.find(type => type.type === markerData.type);
            
            collectionModal.classList.add('hidden');
            
            rewardAnimation.innerHTML = '';
            
            const coinElement = document.createElement('div');
            coinElement.className = 'w-full h-full flex items-center justify-center';
            coinElement.innerHTML = `
                <div class="collect-animation">
                    <i class="fas fa-coins text-5xl text-yellow-500"></i>
                </div>
            `;
            rewardAnimation.appendChild(coinElement);
            
            rewardTitle.textContent = "Congratulations!";
            rewardAmount.textContent = eggType.tokenValue;
            rewardDescription.textContent = `You collected ${eggType.tokenValue} tokens from a ${eggType.type} egg!`;
            
            rewardModal.classList.remove('hidden');
            
            gameState.tokens += eggType.tokenValue;
            gameState.xp += eggType.tokenValue / 5;
            gameState.collectedEggs++;
            
            const treasureElement = document.querySelector('.flex.flex-col.text-sm.text-gray-600 > div:first-child span');
            if (treasureElement) {
                treasureElement.textContent = `Found ${gameState.collectedEggs} Treasure`;
            }
            
            updateUI();
            
            const markerToRemove = gameState.eggs.find(marker => 
                marker.getLatLng().lat === markerData.position.lat &&
                marker.getLatLng().lng === markerData.position.lng
            );
            
            if (markerToRemove) {
                map.removeLayer(markerToRemove);
                gameState.eggs = gameState.eggs.filter(m => m !== markerToRemove);
            }
            
            setTimeout(addEggsToMap, 3000);
        }
        
        function updateUI() {
            tokenCountElement.textContent = gameState.tokens.toLocaleString();
            tokenCountElement.classList.add('token-count');
            
            setTimeout(() => {
                tokenCountElement.classList.remove('token-count');
            }, 300);
            
            const progressPercent = (gameState.xp / gameState.nextLevelXp) * 100;
            document.querySelector('.progress-fill').style.width = `${progressPercent}%`;
            document.querySelector('.progress-bar + .flex > span:last-child').textContent = 
                `${Math.floor(gameState.xp)}/${gameState.nextLevelXp} XP`;
            
            if (gameState.xp >= gameState.nextLevelXp) {
                gameState.level++;
                gameState.xp = gameState.xp - gameState.nextLevelXp;
                gameState.nextLevelXp = Math.floor(gameState.nextLevelXp * 1.3);
                document.querySelector('.progress-bar + .flex > span:first-child').textContent = `Level ${gameState.level}`;
            }
        }
        
        function startGame() {
            if (gameState.energy <= 0) {
                alert("Not enough energy! Please wait for energy to recharge.");
                return;
            }

            gameState.energy--;
            updateEnergyUI();

            gameState.isPlaying = true;
            startButton.innerHTML = '<i class="fas fa-running mr-2"></i> Hunting... (20:00)';
            startButton.classList.remove('from-purple-500', 'to-indigo-600');
            startButton.classList.add('from-green-500', 'to-teal-600');
            
            gameState.huntEndTime = new Date().getTime() + (20 * 60 * 1000);
            
            gameState.huntTimer = setInterval(updateHuntTimer, 1000);
            
            if (!gameState.energyTimer) {
                gameState.energyTimer = setInterval(rechargeEnergy, 60 * 60 * 1000);
            }
            
            simulateMovement();
        }

        function updateHuntTimer() {
            const now = new Date().getTime();
            const remaining = gameState.huntEndTime - now;
            
            if (remaining <= 0) {
                clearInterval(gameState.huntTimer);
                endHunt();
                return;
            }
            
            const minutes = Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((remaining % (1000 * 60)) / 1000);
            
            startButton.innerHTML = `<i class="fas fa-running mr-2"></i> Hunting... (${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')})`;
        }

        function endHunt() {
            gameState.isPlaying = false;
            startButton.innerHTML = '<i class="fas fa-play mr-2"></i> Start Hunting';
            startButton.classList.remove('from-green-500', 'to-teal-600');
            startButton.classList.add('from-purple-500', 'to-indigo-600');
            
            clearInterval(gameState.huntTimer);
        }

        function rechargeEnergy() {
            if (gameState.energy < gameState.maxEnergy) {
                gameState.energy++;
                updateEnergyUI();
                
                if (gameState.energy < gameState.maxEnergy) {
                    startEnergyTimer();
                }
            }
        }

        function updateEnergyUI() {
            const energyElement = document.getElementById('energy-count');
            const timerElement = document.getElementById('energy-timer');
            
            if (energyElement) {
                energyElement.textContent = `${gameState.energy}/${gameState.maxEnergy}`;
                
                if (gameState.energy < gameState.maxEnergy) {
                    timerElement.classList.remove('hidden');
                    startEnergyTimer();
                } else {
                    timerElement.classList.add('hidden');
                    clearInterval(gameState.energyTimer);
                    gameState.energyTimer = null;
                }
            }
        }

        function startEnergyTimer() {
            if (gameState.energyTimer) return;
            
            let timeLeft = 60;
            
            function updateTimer() {
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                
                document.getElementById('energy-timer').textContent = 
                    `(${minutes}:${seconds.toString().padStart(2, '0')})`;
                
                timeLeft--;
                
                if (timeLeft < 0) {
                    clearInterval(gameState.energyTimer);
                    gameState.energyTimer = null;
                    rechargeEnergy();
                }
            }
            
            updateTimer();
            gameState.energyTimer = setInterval(updateTimer, 1000);
        }
        
        function simulateMovement() {
            if (!gameState.isPlaying || !userMarker) return;
            
            const newLat = userMarker.getLatLng().lat + (Math.random() * 0.001 - 0.0005);
            const newLng = userMarker.getLatLng().lng + (Math.random() * 0.001 - 0.0005);
            
            userMarker.setLatLng([newLat, newLng]);
            
            setTimeout(simulateMovement, 2000);
        }
        
        function initNavigation() {
            const navItems = document.querySelectorAll('.nav-item');
            
            navItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetSection = this.getAttribute('data-section');
                    
                    navItems.forEach(nav => nav.classList.remove('active'));
                    this.classList.add('active');
                    
                    document.querySelectorAll('main > div[id$="-section"]').forEach(section => {
                        section.classList.add('hidden');
                    });
                    
                    document.getElementById(targetSection).classList.remove('hidden');
                    
                    const targetElement = document.getElementById(targetSection);
                    targetElement.style.opacity = '0';
                    targetElement.style.transform = 'translateY(20px)';
                    
                    setTimeout(() => {
                        targetElement.style.transition = 'all 0.3s ease';
                        targetElement.style.opacity = '1';
                        targetElement.style.transform = 'translateY(0)';
                    }, 10);
                });
            });
        }
        
        function openChest() {
            const markerData = JSON.parse(openChestButton.dataset.marker);
            
            chestLottie.play();
            
            const reward = Math.floor(Math.random() * 151) + 50;
            
            setTimeout(() => {
                chestModal.classList.add('hidden');
                
                rewardAnimation.innerHTML = '';
                
                const coinElement = document.createElement('div');
                coinElement.className = 'w-full h-full flex items-center justify-center';
                coinElement.innerHTML = `
                    <div class="collect-animation">
                        <i class="fas fa-coins text-5xl text-yellow-500"></i>
                    </div>
                `;
                rewardAnimation.appendChild(coinElement);
                
                rewardTitle.textContent = "Jackpot!";
                rewardAmount.textContent = reward;
                rewardDescription.textContent = `You collected ${reward} tokens from the treasure chest!`;
                
                rewardModal.classList.remove('hidden');
                
                gameState.tokens += reward;
                gameState.xp += reward / 5;
                gameState.collectedEggs++;
                
                updateUI();
                
                const markerToRemove = gameState.eggs.find(marker => 
                    marker.getLatLng().lat === markerData.position.lat &&
                    marker.getLatLng().lng === markerData.position.lng
                );
                
                if (markerToRemove) {
                    map.removeLayer(markerToRemove);
                    gameState.eggs = gameState.eggs.filter(m => m !== markerToRemove);
                }
                
                setTimeout(addEggsToMap, 3000);
            }, 3000);
        }

        // Event Listeners
        startButton.addEventListener('click', startGame);
        openEggButton.addEventListener('click', openEgg);
        openChestButton.addEventListener('click', openChest);
        closeRewardButton.addEventListener('click', () => {
            rewardModal.classList.add('hidden');
        });
        
        // Initialize everything when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initMap();
            initNavigation();
        });
    </script>
</body>
</html>