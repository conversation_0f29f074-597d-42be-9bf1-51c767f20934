// src/core/i18n/config.ts
export const defaultLocale = "en";

export const timeZone = "Europe/Amsterdam";

// Mở rộng danh sách ngôn ngữ hỗ trợ
export const locales = [
    "en", // English
    "vi", // Tiếng Vi<PERSON>
    "ru", // Русский
    "zh", // 中文
    "es", // Español
    "fr", // Français
    "de", // Deutsch
    "it", // Italiano
    "ja", // 日本語
    "ko", // 한국어
    "pt", // Português
    "ar", // العربية
    "hi", // हिन्दी
    "th", // ไทย
    "id", // Bahasa Indonesia
    "tr", // Türkçe
    "pl", // Polski
    "nl", // Nederlands
    "sv", // Svenska
    "da", // Dansk
    "no", // Norsk
    "fi", // Suomi
] as const;

export const localesMap = [
    { key: "en", title: "English", nativeName: "English" },
    { key: "vi", title: "Vietnamese", nativeName: "Tiếng Việt" },
    { key: "ru", title: "Russian", nativeName: "Русский" },
    { key: "zh", title: "Chinese", nativeName: "中文" },
    { key: "es", title: "Spanish", nativeName: "Español" },
    { key: "fr", title: "French", nativeName: "Français" },
    { key: "de", title: "German", nativeName: "Deutsch" },
    { key: "it", title: "Italian", nativeName: "Italiano" },
    { key: "ja", title: "Japanese", nativeName: "日本語" },
    { key: "ko", title: "Korean", nativeName: "한국어" },
    { key: "pt", title: "Portuguese", nativeName: "Português" },
    { key: "ar", title: "Arabic", nativeName: "العربية" },
    { key: "hi", title: "Hindi", nativeName: "हिन्दी" },
    { key: "th", title: "Thai", nativeName: "ไทย" },
    { key: "id", title: "Indonesian", nativeName: "Bahasa Indonesia" },
    { key: "tr", title: "Turkish", nativeName: "Türkçe" },
    { key: "pl", title: "Polish", nativeName: "Polski" },
    { key: "nl", title: "Dutch", nativeName: "Nederlands" },
    { key: "sv", title: "Swedish", nativeName: "Svenska" },
    { key: "da", title: "Danish", nativeName: "Dansk" },
    { key: "no", title: "Norwegian", nativeName: "Norsk" },
    { key: "fi", title: "Finnish", nativeName: "Suomi" },
];

// Mapping từ Telegram language codes sang locale codes
export const telegramToLocaleMap: Record<string, string> = {
    // English variants
    'en': 'en',
    'en_US': 'en',
    'en_GB': 'en',
    'en_CA': 'en',
    'en_AU': 'en',
    
    // Vietnamese
    'vi': 'vi',
    'vi_VN': 'vi',
    
    // Russian
    'ru': 'ru',
    'ru_RU': 'ru',
    
    // Chinese variants
    'zh': 'zh',
    'zh_CN': 'zh',
    'zh_TW': 'zh',
    'zh_HK': 'zh',
    
    // Spanish variants
    'es': 'es',
    'es_ES': 'es',
    'es_MX': 'es',
    'es_AR': 'es',
    'es_CO': 'es',
    
    // French variants
    'fr': 'fr',
    'fr_FR': 'fr',
    'fr_CA': 'fr',
    
    // German variants
    'de': 'de',
    'de_DE': 'de',
    'de_AT': 'de',
    'de_CH': 'de',
    
    // Italian
    'it': 'it',
    'it_IT': 'it',
    
    // Japanese
    'ja': 'ja',
    'ja_JP': 'ja',
    
    // Korean
    'ko': 'ko',
    'ko_KR': 'ko',
    
    // Portuguese variants
    'pt': 'pt',
    'pt_PT': 'pt',
    'pt_BR': 'pt',
    
    // Arabic variants
    'ar': 'ar',
    'ar_SA': 'ar',
    'ar_EG': 'ar',
    'ar_AE': 'ar',
    
    // Hindi
    'hi': 'hi',
    'hi_IN': 'hi',
    
    // Thai
    'th': 'th',
    'th_TH': 'th',
    
    // Indonesian
    'id': 'id',
    'id_ID': 'id',
    
    // Turkish
    'tr': 'tr',
    'tr_TR': 'tr',
    
    // Polish
    'pl': 'pl',
    'pl_PL': 'pl',
    
    // Dutch
    'nl': 'nl',
    'nl_NL': 'nl',
    'nl_BE': 'nl',
    
    // Swedish
    'sv': 'sv',
    'sv_SE': 'sv',
    
    // Danish
    'da': 'da',
    'da_DK': 'da',
    
    // Norwegian
    'no': 'no',
    'nb': 'no',
    'nn': 'no',
    'nb_NO': 'no',
    'nn_NO': 'no',
    
    // Finnish
    'fi': 'fi',
    'fi_FI': 'fi',
};

/**
 * Convert Telegram language code to supported locale
 * @param telegramLangCode - Language code from Telegram initData
 * @returns Supported locale code or default locale
 */
export function mapTelegramLanguageToLocale(telegramLangCode?: string): string {
    if (!telegramLangCode) {
        return defaultLocale;
    }

    // Try exact match first
    const exactMatch = telegramToLocaleMap[telegramLangCode];
    if (exactMatch && locales.includes(exactMatch as any)) {
        return exactMatch;
    }

    // Try language code without country (e.g., 'en' from 'en_US')
    const languageOnly = telegramLangCode.split('_')[0];
    const languageMatch = telegramToLocaleMap[languageOnly];
    if (languageMatch && locales.includes(languageMatch as any)) {
        return languageMatch;
    }

    // Check if language code is directly supported
    if (locales.includes(languageOnly as any)) {
        return languageOnly;
    }

    // Fallback to default locale
    console.log(`Unsupported language: ${telegramLangCode}, falling back to ${defaultLocale}`);
    return defaultLocale;
}