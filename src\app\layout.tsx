import { getLocale } from "next-intl/server";

import { Root } from "@/components/Root/Root";

import { I18nProvider } from "@/core/i18n/provider";

import type { Metadata, Viewport } from "next";
import type { PropsWithChildren } from "react";

import "@telegram-apps/telegram-ui/dist/styles.css";
import "normalize.css/normalize.css";
import "./_assets/globals.css";

export const viewport: Viewport = {
    width: "device-width",
    initialScale: 1.0,
    maximumScale: 1.0,
    userScalable: false
};

export const metadata: Metadata = {
    title: "HuntFi – Explore. Hunt. Earn.",
    description:
        "HuntFi – Explore the map, unlock treasure chests near you, and earn unique crypto rewards. A real-world GameFi x DeFi adventure!",
    keywords: [
        "huntfi",
        "gamefi",
        "defi",
        "crypto rewards",
        "treasure hunt",
        "blockchain game",
        "location-based game",
        "earn crypto",
        "mobile game",
        "telegram mini app",
        "real-world gaming",
        "crypto treasure hunt"
    ],
    openGraph: {
        title: "HuntFi – Explore. Hunt. Earn.",
        description: "Discover treasures around you. Earn while you explore. A real-world GameFi x DeFi adventure!",
        type: "website",
        locale: "en_US"
    },
    twitter: {
        card: "summary_large_image",
        title: "HuntFi – Explore. Hunt. Earn.",
        description: "Discover treasures around you. Earn while you explore."
    }
};

export default async function RootLayout({ children }: PropsWithChildren) {
    const locale = await getLocale();

    return (
        <html lang={locale} suppressHydrationWarning>
            <head>
                <meta name="theme-color" content="#4F46E5" />
                <meta name="apple-mobile-web-app-capable" content="yes" />
                <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
                <meta name="apple-mobile-web-app-title" content="HuntFi" />

                {/* Leaflet CSS */}
                <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />

                {/* Font Awesome */}
                <link
                    rel="stylesheet"
                    href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
                />

                {/* Google Fonts */}
                <link rel="preconnect" href="https://fonts.googleapis.com" />
                <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
                {/* eslint-disable-next-line @next/next/no-page-custom-font */}
                <link
                    href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&display=swap"
                    rel="stylesheet"
                />

                {/* Favicon and App Icons */}
                <link rel="icon" href="/favicon.ico" />
                <link rel="apple-touch-icon" href="/apple-touch-icon.png" />

                {/* Structured Data for GameFi/DeFi */}
                <script
                    type="application/ld+json"
                    dangerouslySetInnerHTML={{
                        __html: JSON.stringify({
                            "@context": "https://schema.org",
                            "@type": "MobileApplication",
                            name: "HuntFi",
                            description:
                                "HuntFi – Explore the map, unlock treasure chests near you, and earn unique crypto rewards. A real-world GameFi x DeFi adventure!",
                            category: "Game",
                            applicationCategory: "GameApplication",
                            operatingSystem: "iOS, Android, Web",
                            offers: {
                                "@type": "Offer",
                                price: "0",
                                priceCurrency: "USD"
                            }
                        })
                    }}
                />
            </head>
            <body style={{ fontFamily: "Poppins, sans-serif" }} suppressHydrationWarning>
                <I18nProvider>
                    <Root>{children}</Root>
                </I18nProvider>
            </body>
        </html>
    );
}
