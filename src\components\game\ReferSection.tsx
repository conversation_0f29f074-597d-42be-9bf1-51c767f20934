"use client";

const ReferSection = () => {
    const referralCode = "EGG-HUNT-123";
    const referrals = 3;

    const handleShare = () => {
        if (navigator.share) {
            navigator.share({
                title: "Egg Hunt - Token Collection Game",
                text: "Join me in this amazing treasure hunting game!",
                url: `${window.location.origin}?ref=${referralCode}`
            });
        } else {
            navigator.clipboard.writeText(`${window.location.origin}?ref=${referralCode}`);
            alert("Referral link copied to clipboard!");
        }
    };

    return (
        <div className="w-full h-full p-6 overflow-y-auto">
            <h2 className="text-2xl font-bold mb-6 text-gray-800">Refer Friends</h2>

            <div className="space-y-6">
                <div className="bg-white rounded-xl p-6 shadow-md text-center border border-gray-100">
                    <div className="w-20 h-20 mx-auto bg-purple-100 rounded-full flex items-center justify-center mb-4">
                        <i className="fas fa-user-friends text-3xl text-purple-600"></i>
                    </div>
                    <h3 className="font-bold text-lg mb-2">Invite Friends & Earn!</h3>
                    <p className="text-gray-600 mb-4">
                        Invite friends and earn <span className="font-bold text-yellow-600">100 bonus tokens</span> when
                        they join!
                    </p>
                    <button
                        onClick={handleShare}
                        className="bg-gradient-to-r from-purple-500 to-indigo-600 text-white font-bold py-3 px-6 rounded-full w-full hover:shadow-lg transition-all">
                        <i className="fas fa-share mr-2"></i>
                        Share Invite Link
                    </button>
                </div>

                <div className="bg-white rounded-xl p-6 shadow-md border border-gray-100">
                    <h3 className="font-bold text-lg mb-4">Your Referral Stats</h3>

                    <div className="space-y-4">
                        <div className="flex justify-between items-center">
                            <span className="text-gray-600">Total Referrals</span>
                            <span className="font-bold text-xl text-purple-600">{referrals}</span>
                        </div>

                        <div className="flex justify-between items-center">
                            <span className="text-gray-600">Bonus Earned</span>
                            <span className="font-bold text-xl text-yellow-600">{referrals * 100}</span>
                        </div>

                        <div className="pt-4 border-t border-gray-200">
                            <div className="text-sm text-gray-600 mb-2">Your Referral Code</div>
                            <div className="bg-gray-100 rounded-lg p-3 font-mono text-center">{referralCode}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ReferSection;
