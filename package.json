{"name": "nextjs-template", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:https": "next dev --experimental-https", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "eslint . --fix --ext .ts,.tsx"}, "dependencies": {"@supabase/supabase-js": "^2.50.5", "@telegram-apps/sdk-react": "^3.2.4", "@telegram-apps/telegram-ui": "^2.1.5", "@tonconnect/ui-react": "^2.1.0", "clsx": "^2.0.0", "eruda": "^3.0.1", "leaflet": "^1.9.4", "lottie-react": "^2.4.1", "next": "15.3.1", "next-intl": "^4.1.0", "normalize.css": "^8.0.1", "react": "^18", "react-dom": "^18", "react-leaflet": "^4.2.1", "zustand": "^4.4.7"}, "devDependencies": {"@types/leaflet": "^1.9.8", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "eslint": "^8", "eslint-config-next": "14.2.4", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "postcss": "^8", "prettier": "^3.3.3", "tailwindcss": "^3.4.1", "typescript": "^5"}}