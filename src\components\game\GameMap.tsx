// src/components/game/GameMap.tsx
"use client";

import { useEffect, useRef, useState } from "react";

import { useGameStore } from "@/store/gameStore";
import { GameEgg, GamePosition } from "@/types/game";
import { LocationManager } from "./LocationManager";

interface GameMapProps {
    onEggCollect: (eggId: string) => void;
}

export function GameMap({ onEggCollect }: GameMapProps) {
    const mapRef = useRef<HTMLDivElement>(null);
    const mapInstanceRef = useRef<any>(null);
    const userMarkerRef = useRef<any>(null);
    const eggMarkersRef = useRef<any[]>([]);

    const { eggs, userPosition, setUserPosition, isPlaying } = useGameStore();
    const [isMapReady, setIsMapReady] = useState(false);
    const [hasLocationPermission, setHasLocationPermission] = useState(false);
    const [mapError, setMapError] = useState<string | null>(null);

    // Check if user already has location permission
    useEffect(() => {
        if (userPosition) {
            setHasLocationPermission(true);
            // Add delay to ensure DOM is ready
            setTimeout(() => {
                if (mapRef.current) {
                    initializeMap(userPosition);
                }
            }, 100);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [userPosition]);

    const handleLocationGranted = (position: GamePosition) => {
        setUserPosition(position);
        setHasLocationPermission(true);
        // Add delay to ensure DOM is ready
        setTimeout(() => {
            if (mapRef.current) {
                initializeMap(position);
                // Force spawn eggs after map initialization
                setTimeout(() => {
                    const { spawnEggs } = useGameStore.getState();
                    spawnEggs();
                }, 300);
            }
        }, 100);
    };

    const handleLocationDenied = () => {
        setHasLocationPermission(false);
        setMapError("Location access denied");
    };

    // Initialize map when location is granted
    const initializeMap = async (position: GamePosition) => {
        if (typeof window === "undefined") {
            return;
        }

        if (!mapRef.current) {
            return;
        }

        // If map is already initialized and ready, don't reinitialize
        if (mapInstanceRef.current && isMapReady) {
            return;
        }

        try {
            setMapError(null);

            // Load Leaflet dynamically
            const leaflet = await import("leaflet");
            const LeafletModule = leaflet.default;

            setupMap(LeafletModule, [position.lat, position.lng]);
        } catch (error) {
            setMapError(`Failed to load map: ${error instanceof Error ? error.message : String(error)}`);

            // Show fallback message
            if (mapRef.current) {
                mapRef.current.innerHTML = `
                    <div class="absolute inset-0 flex items-center justify-center z-10 bg-gray-100 p-4">
                        <div class="text-center max-w-sm">
                            <div class="text-5xl mb-4">❌</div>
                            <h3 class="text-xl font-bold mb-2 text-gray-800">Map Loading Failed</h3>
                            <p class="text-gray-600 mb-4">Error: ${error instanceof Error ? error.message : String(error)}</p>
                            <button onclick="window.location.reload()" 
                                    class="bg-purple-500 text-white font-bold py-2 px-4 rounded-full hover:bg-purple-600">
                                Reload Page
                            </button>
                        </div>
                    </div>
                `;
            }
        }
    };

    const setupMap = (LeafletModule: any, position: [number, number]) => {
        if (!mapRef.current) {
            return;
        }

        try {
            // Clean up existing map
            if (mapInstanceRef.current) {
                mapInstanceRef.current.remove();
                mapInstanceRef.current = null;
            }

            if ((mapRef.current as any)._leaflet_id) {
                delete (mapRef.current as any)._leaflet_id;
            }

            // Create new map
            const map = LeafletModule.map(mapRef.current, {
                center: position,
                zoom: 15,
                zoomControl: true,
                scrollWheelZoom: true,
                touchZoom: true,
                doubleClickZoom: true,
                boxZoom: true,
                keyboard: true,
                dragging: true,
                trackResize: true
            });

            LeafletModule.tileLayer("https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png", {
                minZoom: 0,
                maxZoom: 20,
                attribution:
                    '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
                subdomains: "abcd"
            }).addTo(map);

            // Create user marker
            const userMarkerElement = document.createElement("div");
            userMarkerElement.className =
                "w-8 h-8 rounded-full bg-purple-500 border-4 border-white shadow-lg animate-pulse";

            const userMarker = LeafletModule.marker(position, {
                icon: LeafletModule.divIcon({
                    html: userMarkerElement.outerHTML,
                    className: "user-marker-container",
                    iconSize: [32, 32],
                    iconAnchor: [16, 16]
                }),
                zIndexOffset: 100
            }).addTo(map);

            // Store references
            mapInstanceRef.current = map;
            userMarkerRef.current = userMarker;
            setIsMapReady(true);
            setMapError(null);

            // Spawn eggs when map is ready and position is set
            setTimeout(() => {
                const { spawnEggs, eggs } = useGameStore.getState();
                if (eggs.length === 0) {
                    spawnEggs();
                }
            }, 500);
        } catch (error) {
            setMapError(`Map setup failed: ${error instanceof Error ? error.message : String(error)}`);
            setIsMapReady(false);
        }
    };

    // Update egg markers when eggs change
    useEffect(() => {
        if (!mapInstanceRef.current || !isMapReady) {
            return;
        }

        const updateMarkers = async () => {
            try {
                const leaflet = await import("leaflet");
                const LeafletModule = leaflet.default;

                // Clear existing egg markers
                eggMarkersRef.current.forEach((marker) => {
                    mapInstanceRef.current.removeLayer(marker);
                });
                eggMarkersRef.current = [];

                // Add new egg markers
                const uncollectedEggs = eggs.filter((egg) => !egg.collected);

                uncollectedEggs.forEach((egg: GameEgg) => {
                    const eggElement = document.createElement("div");
                    eggElement.className = "w-12 h-12 cursor-pointer transform hover:scale-110 transition-transform";

                    // Use treasure emoji based on egg type
                    const treasureEmoji =
                        egg.eggType.type === "gold" ? "🏆" : egg.eggType.type === "silver" ? "💰" : "🎁";

                    eggElement.innerHTML = `
                        <div class="w-full h-full flex items-center justify-center text-2xl animate-bounce" 
                             style="filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3));">
                          ${treasureEmoji}
                        </div>
                    `;

                    const marker = LeafletModule.marker([egg.position.lat, egg.position.lng], {
                        icon: LeafletModule.divIcon({
                            html: eggElement.outerHTML,
                            className: "egg-marker-container",
                            iconSize: [48, 48],
                            iconAnchor: [24, 24]
                        }),
                        zIndexOffset: 10
                    }).addTo(mapInstanceRef.current);

                    marker.on("click", () => {
                        if (isPlaying) {
                            onEggCollect(egg.id);
                        }
                    });

                    eggMarkersRef.current.push(marker);
                });
            } catch (error) {
                // Silent error handling
            }
        };

        updateMarkers();
    }, [eggs, isMapReady, isPlaying, onEggCollect]);

    // Simulate user movement when playing
    useEffect(() => {
        if (!isPlaying || !userMarkerRef.current || !userPosition) {
            return;
        }

        const moveInterval = setInterval(() => {
            const newLat = userPosition.lat + (Math.random() * 0.001 - 0.0005);
            const newLng = userPosition.lng + (Math.random() * 0.001 - 0.0005);

            userMarkerRef.current.setLatLng([newLat, newLng]);
            setUserPosition({ lat: newLat, lng: newLng });
        }, 2000);

        return () => {
            clearInterval(moveInterval);
        };
    }, [isPlaying, userPosition, setUserPosition]);

    // Clean up on unmount
    useEffect(() => {
        return () => {
            if (mapInstanceRef.current) {
                mapInstanceRef.current.remove();
                mapInstanceRef.current = null;
            }

            // Clear egg markers
            eggMarkersRef.current.forEach((marker) => {
                if (marker && marker.remove) {
                    marker.remove();
                }
            });
            eggMarkersRef.current = [];

            // Clear user marker reference
            userMarkerRef.current = null;
            setIsMapReady(false);
        };
    }, []);

    // Additional effect to ensure eggs are spawned when map is ready
    useEffect(() => {
        if (isMapReady && userPosition) {
            const { eggs, spawnEggs } = useGameStore.getState();
            // If no eggs exist, spawn them
            if (eggs.length === 0) {
                setTimeout(() => {
                    spawnEggs();
                }, 200);
            }
        }
    }, [isMapReady, userPosition]);

    // Show LocationManager if no permission yet
    if (!hasLocationPermission) {
        return <LocationManager onLocationGranted={handleLocationGranted} onLocationDenied={handleLocationDenied} />;
    }

    return (
        <div className="w-full h-full relative">
            <div ref={mapRef} className="w-full h-full z-0" />

            {/* Loading overlay */}
            {!isMapReady && !mapError && (
                <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4"></div>
                        <p className="text-gray-600">Loading map...</p>
                        <p className="text-sm text-gray-500 mt-2">
                            Position:{" "}
                            {userPosition
                                ? `${userPosition.lat.toFixed(4)}, ${userPosition.lng.toFixed(4)}`
                                : "Unknown"}
                        </p>
                    </div>
                </div>
            )}

            {/* Error overlay */}
            {mapError && (
                <div className="absolute inset-0 flex items-center justify-center bg-red-50 p-4">
                    <div className="text-center max-w-sm">
                        <div className="text-5xl mb-4">❌</div>
                        <h3 className="text-xl font-bold mb-2 text-red-800">Map Error</h3>
                        <p className="text-red-600 mb-4 text-sm">{mapError}</p>
                        <button
                            onClick={() => {
                                setMapError(null);
                                if (userPosition) {
                                    initializeMap(userPosition);
                                }
                            }}
                            className="bg-red-500 text-white font-bold py-2 px-4 rounded-full hover:bg-red-600 mr-2">
                            Retry
                        </button>
                        <button
                            onClick={() => window.location.reload()}
                            className="bg-gray-500 text-white font-bold py-2 px-4 rounded-full hover:bg-gray-600">
                            Reload
                        </button>
                    </div>
                </div>
            )}
        </div>
    );
}
