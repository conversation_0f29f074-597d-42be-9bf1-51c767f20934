// src/components/game/ProfileSection.tsx
"use client";

import { useUserSync } from "@/hooks/useUserSync";
import { useEffect, useState } from "react";

interface WeeklyData {
    day: string;
    value: number;
    color: string;
}

const ProfileSection = () => {
    const { user, loading } = useUserSync();
    const [weeklyData, setWeeklyData] = useState<WeeklyData[]>([]);
    const [maskLoaded, setMaskLoaded] = useState(false);

    // Mock weekly data - có thể thay thế bằng dữ liệu thật từ Supabase
    useEffect(() => {
        const mockWeeklyData: WeeklyData[] = [
            { day: "Mon", value: 0, color: "#8b5cf6" },
            { day: "Tue", value: 0, color: "#f97316" },
            { day: "Wed", value: 0, color: "#f97316" },
            { day: "Thu", value: 0, color: "#8b5cf6" },
            { day: "Fri", value: 0, color: "#ec4899" },
            { day: "Sat", value: 0, color: "#f97316" },
            { day: "Sun", value: 0, color: "#8b5cf6" }
        ];
        setWeeklyData(mockWeeklyData);
    }, []);

    // Ensure mask styles are properly loaded
    useEffect(() => {
        // Force a reflow to ensure CSS mask is applied
        const timer = setTimeout(() => {
            setMaskLoaded(true);
        }, 100);
        return () => clearTimeout(timer);
    }, []);

    // Loading screen với màu giống GameHeader và animated background
    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-purple-600 via-indigo-600 to-blue-600 relative overflow-hidden">
                {/* Animated background elements giống GameHeader */}
                <div className="absolute inset-0 opacity-20">
                    {/* Floating circles với animation */}
                    <div className="absolute top-4 left-4 w-8 h-8 rounded-full bg-yellow-400 animate-pulse"></div>
                    <div
                        className="absolute top-8 right-8 w-6 h-6 rounded-full bg-green-400 animate-bounce"
                        style={{ animationDelay: "0.5s" }}></div>
                    <div
                        className="absolute bottom-4 left-1/3 w-4 h-4 rounded-full bg-pink-400 animate-pulse"
                        style={{ animationDelay: "1s" }}></div>

                    {/* Thêm nhiều elements cho hiệu ứng phong phú hơn */}
                    <div
                        className="absolute top-1/3 right-1/4 w-5 h-5 rounded-full bg-cyan-400 animate-bounce"
                        style={{ animationDelay: "1.5s" }}></div>
                    <div
                        className="absolute bottom-1/3 left-1/4 w-7 h-7 rounded-full bg-orange-400 animate-pulse"
                        style={{ animationDelay: "0.3s" }}></div>

                    {/* Floating geometric shapes */}
                    <div
                        className="absolute top-1/2 left-8 w-6 h-6 bg-purple-300 transform rotate-45 animate-spin"
                        style={{ animationDuration: "3s" }}></div>
                    <div
                        className="absolute bottom-8 right-1/3 w-5 h-5 bg-blue-300 transform rotate-12 animate-ping"
                        style={{ animationDelay: "0.8s" }}></div>
                </div>

                {/* Loading spinner với style GameFi */}
                <div className="relative z-10 flex flex-col items-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mb-4"></div>
                    <p className="text-white/90 text-lg font-medium animate-pulse">Loading Profile...</p>
                </div>
            </div>
        );
    }

    if (!user) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-purple-600 via-indigo-600 to-blue-600">
                <p className="text-white">Unable to load user information</p>
            </div>
        );
    }

    // Helper function để format numbers
    const formatNumber = (num: number) => {
        return num.toLocaleString();
    };

    return (
        <div className="min-h-screen bg-white relative">
            {/* Top Gradient Section với animated background elements */}
            <div className="relative bg-gradient-to-br from-purple-600 via-indigo-600 to-blue-600 pt-16 pb-20 px-6 overflow-hidden">
                {/* Animated background elements cho header profile section */}
                <div className="absolute inset-0 opacity-20">
                    {/* Main floating elements giống GameHeader */}
                    <div className="absolute top-4 left-4 w-8 h-8 rounded-full bg-yellow-400 animate-pulse"></div>
                    <div
                        className="absolute top-8 right-8 w-6 h-6 rounded-full bg-green-400 animate-bounce"
                        style={{ animationDelay: "0.5s" }}></div>
                    <div
                        className="absolute bottom-4 left-1/3 w-4 h-4 rounded-full bg-pink-400 animate-pulse"
                        style={{ animationDelay: "1s" }}></div>

                    {/* Additional animated elements cho profile header */}
                    <div
                        className="absolute top-12 left-1/2 w-5 h-5 rounded-full bg-cyan-300 animate-bounce"
                        style={{ animationDelay: "0.2s" }}></div>
                    <div
                        className="absolute top-1/4 right-12 w-6 h-6 rounded-full bg-orange-300 animate-pulse"
                        style={{ animationDelay: "0.7s" }}></div>
                    <div
                        className="absolute bottom-8 right-8 w-4 h-4 rounded-full bg-emerald-400 animate-bounce"
                        style={{ animationDelay: "1.2s" }}></div>

                    {/* Geometric floating shapes */}
                    <div
                        className="absolute top-1/3 left-8 w-5 h-5 bg-purple-300 transform rotate-45 animate-spin"
                        style={{ animationDuration: "4s" }}></div>
                    <div
                        className="absolute bottom-1/4 right-1/4 w-4 h-4 bg-blue-300 transform rotate-12 animate-ping"
                        style={{ animationDelay: "0.9s" }}></div>
                    <div
                        className="absolute top-2/3 left-1/4 w-6 h-6 bg-indigo-300 rounded-full animate-pulse"
                        style={{ animationDelay: "1.4s" }}></div>

                    {/* Sparkle effects */}
                    <div
                        className="absolute top-6 right-1/3 text-yellow-300 animate-ping"
                        style={{ animationDelay: "0.3s" }}>
                        ✨
                    </div>
                    <div
                        className="absolute bottom-12 left-8 text-pink-300 animate-bounce"
                        style={{ animationDelay: "0.8s" }}>
                        ⭐
                    </div>
                    <div
                        className="absolute top-1/2 right-4 text-cyan-300 animate-pulse"
                        style={{ animationDelay: "1.1s" }}>
                        💎
                    </div>

                    {/* Floating waves pattern */}
                    <div className="absolute inset-0 opacity-10">
                        <div
                            className="absolute w-32 h-32 rounded-full bg-white/20 -top-16 -left-16 animate-pulse"
                            style={{ animationDuration: "3s" }}></div>
                        <div
                            className="absolute w-24 h-24 rounded-full bg-white/15 top-1/3 -right-12 animate-pulse"
                            style={{ animationDuration: "4s", animationDelay: "1s" }}></div>
                        <div
                            className="absolute w-40 h-40 rounded-full bg-white/10 -bottom-20 left-1/4 animate-pulse"
                            style={{ animationDuration: "5s", animationDelay: "0.5s" }}></div>
                    </div>
                </div>
            </div>

            {/* White Content Section */}
            <div className="bg-white px-6 pt-10 relative">
                {/* Enhanced Curved bottom border with avatar cutout */}
                <div className="absolute bottom-0 left-0 right-0 h-24 top-[-60px]">
                    {/* Main curved section with circular cutout for avatar - using CSS class with state management */}
                    <div
                        className={`absolute bottom-0 left-0 right-0 h-[100px] bg-white rounded-t-[3rem] ${maskLoaded ? "avatar-cutout-mask" : ""}`}></div>

                    {/* Avatar positioned in the cutout area */}
                    <div className="absolute top-[-40px] left-1/2 transform -translate-x-1/2 z-30">
                        <div className="relative">
                            {user.photo_url ? (
                                <img
                                    src={user.photo_url}
                                    alt="Profile"
                                    className="w-24 h-24 rounded-full border-4 border-white shadow-2xl"
                                />
                            ) : (
                                <div className="w-24 h-24 rounded-full bg-gradient-to-br from-yellow-400 to-orange-500 flex items-center justify-center border-4 border-white shadow-2xl">
                                    <span className="text-white text-2xl font-bold">
                                        {user.first_name?.charAt(0) || "H"}
                                    </span>
                                </div>
                            )}
                            {/* Glow effect around avatar */}
                            <div className="absolute inset-0 rounded-full bg-gradient-to-br from-yellow-400/20 to-orange-500/20 blur-xl scale-110 -z-10"></div>
                        </div>
                    </div>
                </div>
                {/* User Name and Level - nằm trong white section */}
                <div className="text-center mb-8">
                    <h2 className="text-2xl font-bold text-gray-800 mb-4">
                        {user.first_name} {user.last_name || ""}
                    </h2>

                    {/* Level Badge */}
                    <div className="flex justify-center">
                        <div className="flex items-center space-x-2 bg-yellow-100 rounded-full px-4 py-2">
                            <span className="text-yellow-500 text-lg">🏆</span>
                            <span className="text-gray-800 font-semibold">Level {user.level}</span>
                        </div>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-2 gap-4 mb-8">
                    {/* Balance Card */}
                    <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-4 border border-blue-100">
                        <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium text-gray-600">Balance</span>
                            <span className="text-2xl">💰</span>
                        </div>
                        <p className="text-2xl font-bold text-gray-800">{formatNumber(user.balance)}</p>
                        <p className="text-xs text-gray-500">HUFI Tokens</p>
                    </div>

                    {/* Rank Card */}
                    <div className="bg-gradient-to-br from-orange-50 to-yellow-50 rounded-2xl p-4 border border-orange-100">
                        <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium text-gray-600">Global Rank</span>
                            <span className="text-2xl">🌟</span>
                        </div>
                        <p className="text-2xl font-bold text-gray-800">#1,247</p>
                        <p className="text-xs text-gray-500">Top 95% earner</p>
                    </div>
                </div>

                {/* Achievement Banner */}
                <div className="bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl p-4 mb-8 text-white">
                    <div className="flex items-center justify-between">
                        <div>
                            <h3 className="text-lg font-bold mb-1">🎮 GameFi Hunter</h3>
                            <p className="text-white/90 text-sm">You are in the top 95% of HUFI earners!</p>
                        </div>
                        <div className="text-4xl">🏆</div>
                    </div>
                </div>

                {/* Progress Sections */}
                <div className="space-y-6">
                    {/* Level Progress */}
                    <div className="bg-gray-50 rounded-2xl p-4">
                        <div className="flex justify-between items-center mb-3">
                            <span className="text-sm font-semibold text-gray-800">Level Progress</span>
                            <span className="text-xs text-gray-500">
                                {user.level_progress}/100 ({100 - user.level_progress} to next)
                            </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-3">
                            <div
                                className="bg-gradient-to-r from-purple-500 to-pink-500 h-3 rounded-full transition-all duration-500"
                                style={{ width: `${user.level_progress}%` }}
                            />
                        </div>
                    </div>

                    {/* Energy Progress */}
                    <div className="bg-gray-50 rounded-2xl p-4">
                        <div className="flex justify-between items-center mb-3">
                            <span className="text-sm font-semibold text-gray-800">Energy</span>
                            <span className="text-xs text-gray-500">
                                {user.energy}/{user.energy_total}
                            </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-3">
                            <div
                                className="bg-gradient-to-r from-yellow-400 to-orange-500 h-3 rounded-full transition-all duration-500"
                                style={{ width: `${(user.energy / user.energy_total) * 100}%` }}
                            />
                        </div>
                    </div>
                </div>

                {/* Bottom spacing cho navigation */}
                <div className="h-20"></div>
            </div>
        </div>
    );
};

export default ProfileSection;
