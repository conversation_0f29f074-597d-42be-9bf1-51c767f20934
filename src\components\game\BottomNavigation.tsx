"use client";

import { NavigationTab, useHashRouting } from "@/hooks/useHashRouting";

export type { NavigationTab };

const navigationItems = [
    {
        id: "tasks" as NavigationTab,
        icon: "fas fa-tasks",
        label: "Tasks",
        href: "#tasks"
    },
    {
        id: "leaderboard" as NavigationTab,
        icon: "fas fa-trophy",
        label: "League",
        href: "#leaderboard"
    },
    {
        id: "hunt" as NavigationTab,
        icon: "fas fa-running",
        label: "Hunt",
        href: "#hunt",
        isCenter: true
    },
    {
        id: "refer" as NavigationTab,
        icon: "fas fa-user-friends",
        label: "Refer",
        href: "#refer"
    },
    {
        id: "profile" as NavigationTab,
        icon: "fas fa-user",
        label: "Profile",
        href: "#profile"
    }
];

function cn(...classes: (string | boolean | undefined)[]): string {
    return classes.filter(Boolean).join(" ");
}

export function BottomNavigation() {
    const { isHashActive, handleNavigationClick } = useHashRouting();

    return (
        <nav className="nav-container px-4 py-3 bg-white/95 backdrop-blur-lg border-t border-white/20 rounded-t-2xl shadow-lg">
            <style jsx>{`
                .nav-container {
                    box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.1);
                }
            `}</style>

            <div className="flex justify-around items-end">
                {navigationItems.map((item) => {
                    const itemHash = item.href.slice(1);
                    const isActive = isHashActive(itemHash);

                    return (
                        <a
                            key={item.id}
                            href={item.href}
                            onClick={(e) => {
                                handleNavigationClick(itemHash);
                            }}
                            className={cn(
                                "nav-item relative flex flex-col items-center p-2 rounded-xl transition-all duration-300 cursor-pointer no-underline",
                                item.isCenter && "nav-center -mt-5",
                                isActive && "active"
                            )}>
                            <div
                                className={cn(
                                    "nav-icon flex items-center justify-center rounded-2xl transition-all duration-300",
                                    item.isCenter
                                        ? "w-16 h-16 bg-gradient-to-br from-purple-500 to-indigo-600 border-4 border-white shadow-xl"
                                        : "w-12 h-12 bg-purple-50",
                                    isActive &&
                                        !item.isCenter &&
                                        "bg-gradient-to-br from-purple-500 to-indigo-600 shadow-lg scale-105",
                                    !isActive && !item.isCenter && "border-2 border-transparent"
                                )}>
                                <i
                                    className={cn(
                                        item.icon,
                                        item.isCenter ? "text-white text-xl" : "text-base",
                                        isActive && !item.isCenter ? "text-white scale-110" : "text-gray-500",
                                        item.isCenter && "text-white"
                                    )}></i>
                            </div>

                            <span
                                className={cn(
                                    "nav-label text-xs font-medium mt-1 transition-all duration-300",
                                    item.isCenter && "mt-2",
                                    isActive ? "text-purple-600 font-semibold" : "text-gray-500",
                                    item.isCenter && "text-purple-600 font-semibold"
                                )}>
                                {item.label}
                            </span>
                        </a>
                    );
                })}
            </div>
        </nav>
    );
}
