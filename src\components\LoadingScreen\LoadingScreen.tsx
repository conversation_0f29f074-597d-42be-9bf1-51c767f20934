// src/components/LoadingScreen/LoadingScreen.tsx
'use client';

import { useEffect, useState, useCallback, useRef } from 'react';
import { useTranslations } from 'next-intl';

interface LoadingScreenProps {
  onLoadingComplete?: () => void;
  minLoadingTime?: number;
  showProgress?: boolean;
}

export function LoadingScreen({ 
  onLoadingComplete, 
  minLoadingTime = 3000, 
  showProgress = true 
}: LoadingScreenProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [progress, setProgress] = useState(0);
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  const [fadeOut, setFadeOut] = useState(false);
  const [showContent, setShowContent] = useState(false);
  
  const progressRef = useRef(0);
  const messageTimerRef = useRef<NodeJS.Timeout>();

  // Use translations
  const t = useTranslations('loading');

  const messages = [
    t('messages.starting'),
    t('messages.loadingMap'),
    t('messages.preparingTreasures'),
    t('messages.settingLocation'),
    t('messages.preparingAdventure'),
    t('messages.connectingDefi')
  ];

  // Show content with delay for smooth animation
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowContent(true);
    }, 200);
    return () => clearTimeout(timer);
  }, []);

  // Smooth progress animation using requestAnimationFrame
  useEffect(() => {
    if (!showProgress) return;

    let animationFrame: number;
    const startTime = Date.now();
    const duration = minLoadingTime - 500; // Reserve 500ms for fade out

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const rawProgress = Math.min(elapsed / duration, 1);
      
      // Use easeOutCubic for smooth deceleration
      const easedProgress = 1 - Math.pow(1 - rawProgress, 3);
      const newProgress = easedProgress * 100;
      
      progressRef.current = newProgress;
      setProgress(newProgress);

      if (rawProgress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [showProgress, minLoadingTime]);

  // Change messages smoothly based on progress
  useEffect(() => {
    const newIndex = Math.min(
      Math.floor((progress / 100) * messages.length),
      messages.length - 1
    );
    
    if (newIndex !== currentMessageIndex) {
      // Clear existing timer
      if (messageTimerRef.current) {
        clearTimeout(messageTimerRef.current);
      }
      
      // Smooth transition between messages
      messageTimerRef.current = setTimeout(() => {
        setCurrentMessageIndex(newIndex);
      }, 100);
    }
  }, [progress, messages.length, currentMessageIndex]);

  // Handle loading completion
  const completeLoading = useCallback(() => {
    setFadeOut(true);
    setTimeout(() => {
      setIsLoading(false);
      onLoadingComplete?.();
    }, 500); // Shorter fade out duration
  }, [onLoadingComplete]);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (progress >= 99) {
        completeLoading();
      }
    }, minLoadingTime);

    return () => clearTimeout(timer);
  }, [minLoadingTime, progress, completeLoading]);

  // Cleanup timers
  useEffect(() => {
    return () => {
      if (messageTimerRef.current) {
        clearTimeout(messageTimerRef.current);
      }
    };
  }, []);

  if (!isLoading) {
    return null;
  }

  return (
    <div 
      className={`fixed inset-0 z-[9999] flex items-center justify-center transition-all duration-500 ease-out ${
        fadeOut ? 'opacity-0 scale-95' : 'opacity-100 scale-100'
      }`} 
      style={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 25%, #8b5cf6 50%, #ec4899 75%, #f59e0b 100%)',
        willChange: 'opacity, transform'
      }}
    >
      {/* Simplified Background Effects */}
      <div className="absolute inset-0 overflow-hidden">
        
        {/* Subtle grid pattern */}
        <div 
          className="absolute inset-0 opacity-5"
          style={{
            backgroundImage: `
              linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
            `,
            backgroundSize: '60px 60px'
          }} 
        />
      </div>

      <div className={`flex flex-col items-center max-w-sm mx-auto px-6 text-center relative z-10 transition-all duration-700 ease-out ${
        showContent ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
      }`}>
        
        {/* Simplified Logo */}
        <div className="mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 via-orange-500 to-red-500 rounded-2xl flex items-center justify-center shadow-xl mr-4 transform-gpu">
              <div className="text-3xl">🎯</div>
            </div>
            <div className="text-left">
              <h1 className="text-4xl font-black text-white tracking-tight">
                {t('title')}
              </h1>
              <div className="flex items-center mt-2 space-x-2">
                <div className="bg-gradient-to-r from-purple-500 to-violet-500 rounded-full px-2 py-1 text-xs font-bold text-white">
                  GameFi
                </div>
                <div className="bg-gradient-to-r from-green-500 to-emerald-500 rounded-full px-2 py-1 text-xs font-bold text-white">
                  DeFi
                </div>
              </div>
            </div>
          </div>
          <p className="text-white/90 text-lg font-medium">
            {t('subtitle')}
          </p>
        </div>

        {/* Loading Message - Single element to prevent layout shifts */}
        <div className="mb-8 h-16 flex items-center justify-center">
          <div className="flex items-center space-x-3">
            <div className="w-6 h-6 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
              <div className="w-3 h-3 bg-white rounded-full animate-pulse"></div>
            </div>
            <p className="text-lg font-semibold text-white/95 transition-all duration-300 ease-in-out">
              {messages[currentMessageIndex]}
            </p>
          </div>
        </div>

        {/* Optimized Progress Section */}
        {showProgress && (
          <div className="w-full max-w-xs mb-6 space-y-3">
            {/* Progress text */}
            <div className="flex justify-between items-center text-white/90 text-sm font-medium">
              <span>{t('progress')}</span>
              <span className="font-bold bg-white/20 px-2 py-1 rounded-lg text-xs">
                {Math.round(progress)}%
              </span>
            </div>
            
            {/* Smooth Progress bar */}
            <div className="relative h-3 bg-white/20 rounded-full overflow-hidden backdrop-blur-sm">
              <div 
                className="h-full bg-gradient-to-r from-purple-500 via-pink-500 to-yellow-500 rounded-full transition-all duration-100 ease-out transform-gpu"
                style={{ 
                  width: `${Math.min(progress, 100)}%`,
                  willChange: 'width'
                }}
              >
                {/* Single shimmer effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform-gpu animate-shimmer"></div>
              </div>
              
              {/* Simplified glow */}
              <div 
                className="absolute top-0 h-3 bg-gradient-to-r from-purple-400 to-yellow-400 rounded-full blur-sm opacity-40 transition-all duration-100 ease-out transform-gpu"
                style={{ 
                  width: `${Math.min(progress, 100)}%`,
                  willChange: 'width'
                }}
              />
            </div>

            {/* Simplified milestones */}
            <div className="flex justify-between text-xs text-white/60 mt-2">
              <span className={`transition-colors duration-200 ${progress >= 20 ? 'text-green-300' : ''}`}>
                Init
              </span>
              <span className={`transition-colors duration-200 ${progress >= 50 ? 'text-green-300' : ''}`}>
                Map
              </span>
              <span className={`transition-colors duration-200 ${progress >= 80 ? 'text-green-300' : ''}`}>
                DeFi
              </span>
              <span className={`transition-colors duration-200 ${progress >= 95 ? 'text-green-300' : ''}`}>
                Ready
              </span>
            </div>
          </div>
        )}

        {/* Simplified status indicators */}
        <div className="flex items-center space-x-2 mb-4">
          {[0, 1, 2].map((i) => (
            <div
              key={i}
              className="w-2 h-2 bg-white/60 rounded-full transform-gpu"
              style={{
                animation: `bounce 1.5s ease-in-out infinite`,
                animationDelay: `${i * 0.2}s`
              }}
            />
          ))}
        </div>

        {/* Loading Hint */}
        <p className="text-white/70 text-sm font-medium">
          {t('hint')}
        </p>
      </div>

      {/* Optimized CSS animations */}
      <style jsx>{`
        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(200%); }
        }
        .animate-shimmer {
          animation: shimmer 2s infinite ease-in-out;
        }
        @keyframes float-0 {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-8px) rotate(2deg); }
        }
        @keyframes float-1 {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-12px) rotate(-2deg); }
        }
        @keyframes float-2 {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-10px) rotate(1deg); }
        }
        @keyframes bounce {
          0%, 80%, 100% { 
            transform: scale(1);
            opacity: 0.6;
          }
          40% { 
            transform: scale(1.2);
            opacity: 1;
          }
        }
      `}</style>
    </div>
  );
}