// src/hooks/useUserSync.ts
import { useEffect, useState } from 'react';
import { initData, useSignal } from '@telegram-apps/sdk-react';
import { supabase, type DatabaseUser } from '@/lib/supabase';

// Extend User type để include auth_date cho development
interface ExtendedTelegramUser {
  id: number;
  first_name: string;
  last_name?: string;
  username?: string;
  photo_url?: string;
  language_code?: string;
  auth_date?: number; 
  premium?: boolean;
}

interface UserSyncState {
  loading: boolean;
  error: string | null;
  user: DatabaseUser | null;
  action: 'created' | 'updated' | 'no_changes' | null;
}

export function useUserSync() {
  const [state, setState] = useState<UserSyncState>({
    loading: false,
    error: null,
    user: null,
    action: null
  });

  // Lấy init data từ Telegram
  const initDataState = useSignal(initData.state);
  const initDataRaw = useSignal(initData.raw);

  useEffect(() => {
    // Chỉ thực hiện khi có đủ dữ liệu từ Telegram
    if (!initDataState?.user?.id || !initDataState?.user?.first_name) {
      console.log('⏳ Waiting for complete user data...', {
        hasInitDataState: !!initDataState,
        hasUser: !!initDataState?.user,
        hasId: !!initDataState?.user?.id,
        hasFirstName: !!initDataState?.user?.first_name,
        initDataRaw: !!initDataRaw
      });
      return;
    }

    const syncUser = async () => {
      setState(prev => ({ ...prev, loading: true, error: null }));

      try {
        console.log('🔄 Syncing user with database...', {
          userId: initDataState.user?.id,
          firstName: initDataState.user?.first_name
        });

        // Type assertion since we already checked for null above
        const user = initDataState.user!;

        const telegramUser: ExtendedTelegramUser = {
          id: user.id,
          first_name: user.first_name,
          last_name: user.last_name,
          username: user.username,
          photo_url: user.photo_url,
          language_code: user.language_code,
          premium: user.is_premium,
          // Sử dụng auth_date từ initData hoặc current timestamp cho development
          auth_date: Math.floor(Date.now() / 1000)
        };

        const response = await fetch('/api/user', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            user: telegramUser,
            raw: initDataRaw
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `HTTP ${response.status}`);
        }

        const data = await response.json();
        
        console.log('✅ User sync successful:', data);

        setState({
          loading: false,
          error: null,
          user: data.user,
          action: data.action
        });

      } catch (error) {
        console.error('❌ User sync failed:', error);
        setState(prev => ({
          ...prev,
          loading: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        }));
      }
    };

    syncUser();
  }, [initDataState, initDataRaw]);

  // Setup realtime subscription for user updates
  useEffect(() => {
    if (!state.user?.id) return;

    console.log('🔄 Setting up realtime subscription for user:', state.user.id);

    const channel = supabase
      .channel('user-changes')
      .on(
        'postgres_changes',
        {
          event: '*', // Listen to all events (INSERT, UPDATE, DELETE)
          schema: 'public',
          table: 'users',
          filter: `id=eq.${state.user.id}`
        },
        (payload) => {
          console.log('🔴 Realtime user update:', payload);
          
          if (payload.eventType === 'UPDATE' && payload.new) {
            setState(prev => ({
              ...prev,
              user: payload.new as DatabaseUser
            }));
          }
        }
      )
      .subscribe((status) => {
        console.log('📡 Realtime subscription status:', status);
      });

    return () => {
      console.log('🔴 Cleaning up realtime subscription');
      supabase.removeChannel(channel);
    };
  }, [state.user?.id]);

  // Function để refresh user data (với energy được cập nhật)
  const refreshUser = async () => {
    if (!state.user?.id) return;

    try {
      setState(prev => ({ ...prev, loading: true }));

      const response = await fetch(`/api/user?id=${state.user.id}`);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      setState(prev => ({
        ...prev,
        loading: false,
        user: data.user,
        error: null
      }));

    } catch (error) {
      console.error('Error refreshing user:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Refresh failed'
      }));
    }
  };

  return {
    ...state,
    refreshUser
  };
}