// src/app/api/user/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdmin, type TelegramUser, type DatabaseUser } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    console.log('📥 API received body:', JSON.stringify(body, null, 2));
    
    // Validate dữ liệu đầu vào
    if (!body.user) {
      console.error('❌ Missing user object in request body');
      return NextResponse.json(
        { error: 'Missing user object', received: body },
        { status: 400 }
      );
    }
    
    if (!body.user.id) {
      console.error('❌ Missing user.id:', body.user);
      return NextResponse.json(
        { error: 'Missing user.id', user: body.user },
        { status: 400 }
      );
    }
    
    if (!body.user.first_name) {
      console.error('❌ Missing user.first_name:', body.user);
      return NextResponse.json(
        { error: 'Missing user.first_name', user: body.user },
        { status: 400 }
      );
    }
    
    console.log('✅ Valid user data received for development/localhost')

    const telegramUser: TelegramUser = body.user;
    
    // Chuyển đổi Unix timestamp sang ISO string cho database
    const authTimestamp = telegramUser.auth_date 
      ? new Date(telegramUser.auth_date * 1000).toISOString()
      : new Date().toISOString();
    
    console.log('✅ Valid user data received:', {
      id: telegramUser.id,
      first_name: telegramUser.first_name,
      auth_date_unix: telegramUser.auth_date,
      auth_date_iso: authTimestamp
    });

    // Create admin client for server-side operations
    const supabaseAdmin = createSupabaseAdmin();
    
    // Kiểm tra user đã tồn tại chưa
    const { data: existingUser, error: fetchError } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('id', telegramUser.id)
      .single();

    if (fetchError && fetchError.code !== 'PGRST116') {
      // PGRST116 = No rows found, đây là trường hợp bình thường cho user mới
      console.error('Error fetching user:', fetchError);
      return NextResponse.json(
        { error: 'Database error' },
        { status: 500 }
      );
    }

    // Dữ liệu user cơ bản từ Telegram
    const userData = {
      id: telegramUser.id,
      first_name: telegramUser.first_name,
      last_name: telegramUser.last_name || null,
      username: telegramUser.username || null,
      photo_url: telegramUser.photo_url || null,
      language_code: telegramUser.language_code || null,
      auth_date: authTimestamp,
      premium: telegramUser.premium || false,
    };

    if (existingUser) {
      // User đã tồn tại - kiểm tra xem có thay đổi không
      const hasChanges = 
        existingUser.last_name !== userData.last_name ||
        existingUser.username !== userData.username ||
        existingUser.photo_url !== userData.photo_url ||
        existingUser.language_code !== userData.language_code ||
        existingUser.premium !== userData.premium ||
        existingUser.auth_date !== userData.auth_date;

      if (hasChanges) {
        // Cập nhật thông tin user (chỉ cập nhật các trường Telegram, không động vào game data)
        const { data: updatedUser, error: updateError } = await supabaseAdmin
          .from('users')
          .update({
            ...userData,
            updated_at: new Date().toISOString()
          })
          .eq('id', telegramUser.id)
          .select()
          .single();

        if (updateError) {
          console.error('Error updating user:', updateError);
          return NextResponse.json(
            { error: 'Failed to update user' },
            { status: 500 }
          );
        }

        return NextResponse.json({
          success: true,
          user: updatedUser,
          action: 'updated'
        });
      } else {
        // Không có thay đổi - trả về user hiện tại với năng lượng được cập nhật
        const { data: userWithUpdatedEnergy, error: energyError } = await supabaseAdmin
          .rpc('get_user_with_updated_energy', { user_id: telegramUser.id });

        if (energyError || !userWithUpdatedEnergy || userWithUpdatedEnergy.length === 0) {
          console.error('Error getting user with updated energy:', energyError);
          return NextResponse.json({
            success: true,
            user: existingUser,
            action: 'no_changes'
          });
        }

        return NextResponse.json({
          success: true,
          user: userWithUpdatedEnergy[0],
          action: 'no_changes'
        });
      }
    } else {
      // Tạo user mới với các giá trị game mặc định
      const newUserData = {
        ...userData,
        // Game data mặc định
        balance: 0,
        energy_total: 50,
        energy: 50,
        level: 1,
        level_progress: 0,
        last_energy_update: new Date().toISOString()
      };

      const { data: newUser, error: createError } = await supabaseAdmin
        .from('users')
        .insert(newUserData)
        .select()
        .single();

      if (createError) {
        console.error('Error creating user:', createError);
        return NextResponse.json(
          { error: 'Failed to create user' },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        user: newUser,
        action: 'created'
      });
    }

  } catch (error) {
    console.error('Unexpected error in user API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET endpoint để lấy thông tin user với năng lượng được cập nhật
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('id');

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing user ID' },
        { status: 400 }
      );
    }

    // Create admin client for server-side operations
    const supabaseAdmin = createSupabaseAdmin();

    // Sử dụng function để lấy user với năng lượng được cập nhật
    const { data: userWithUpdatedEnergy, error } = await supabaseAdmin
      .rpc('get_user_with_updated_energy', { user_id: parseInt(userId) });

    if (error) {
      console.error('Error getting user with updated energy:', error);
      return NextResponse.json(
        { error: 'Database error' },
        { status: 500 }
      );
    }

    if (!userWithUpdatedEnergy || userWithUpdatedEnergy.length === 0) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      user: userWithUpdatedEnergy[0]
    });

  } catch (error) {
    console.error('Unexpected error in GET user API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}