// src/hooks/useEnergyTimer.ts
import { useEffect, useState } from 'react';
import type { DatabaseUser } from '@/lib/supabase';

interface EnergyTimer {
  timeToNextEnergy: string; // "19:45" format
  timeToFullEnergy: string; // "3h 20m" format
  minutesToNext: number;
  minutesToFull: number;
  isRegenerating: boolean;
}

export function useEnergyTimer(user: DatabaseUser | null): EnergyTimer {
  const [timer, setTimer] = useState<EnergyTimer>({
    timeToNextEnergy: '',
    timeToFullEnergy: '',
    minutesToNext: 0,
    minutesToFull: 0,
    isRegenerating: false
  });

  useEffect(() => {
    if (!user || user.energy >= user.energy_total) {
      setTimer({
        timeToNextEnergy: '',
        timeToFullEnergy: 'Full',
        minutesToNext: 0,
        minutesToFull: 0,
        isRegenerating: false
      });
      return;
    }

    const calculateEnergyTimer = () => {
      const now = new Date().getTime();
      const lastUpdate = new Date(user.last_energy_update).getTime();
      const timeSinceLastUpdate = now - lastUpdate;
      
      // Each energy takes 20 minutes (1200 seconds) to regenerate
      const ENERGY_REGEN_TIME = 20 * 60 * 1000; // 20 minutes in milliseconds
      
      // Calculate time to next energy
      const timeInCurrentCycle = timeSinceLastUpdate % ENERGY_REGEN_TIME;
      const timeToNextEnergyMs = ENERGY_REGEN_TIME - timeInCurrentCycle;
      
      // Calculate how much energy we should have
      const energyToAdd = Math.floor(timeSinceLastUpdate / ENERGY_REGEN_TIME);
      const currentCalculatedEnergy = Math.min(user.energy + energyToAdd, user.energy_total);
      
      if (currentCalculatedEnergy >= user.energy_total) {
        setTimer({
          timeToNextEnergy: '',
          timeToFullEnergy: 'Full',
          minutesToNext: 0,
          minutesToFull: 0,
          isRegenerating: false
        });
        return;
      }

      // Calculate time to full energy
      const energyNeeded = user.energy_total - currentCalculatedEnergy;
      const timeToFullMs = (energyNeeded - 1) * ENERGY_REGEN_TIME + timeToNextEnergyMs;
      
      // Format times
      const formatTime = (ms: number): string => {
        const minutes = Math.floor(ms / (1000 * 60));
        const seconds = Math.floor((ms % (1000 * 60)) / 1000);
        return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
      };

      const formatDuration = (ms: number): string => {
        const totalMinutes = Math.floor(ms / (1000 * 60));
        const hours = Math.floor(totalMinutes / 60);
        const minutes = totalMinutes % 60;
        
        if (hours > 0) {
          return `${hours}h ${minutes}m`;
        }
        return `${minutes}m`;
      };

      setTimer({
        timeToNextEnergy: formatTime(timeToNextEnergyMs),
        timeToFullEnergy: formatDuration(timeToFullMs),
        minutesToNext: Math.floor(timeToNextEnergyMs / (1000 * 60)),
        minutesToFull: Math.floor(timeToFullMs / (1000 * 60)),
        isRegenerating: true
      });
    };

    // Initial calculation
    calculateEnergyTimer();

    // Update every second
    const interval = setInterval(calculateEnergyTimer, 1000);

    return () => clearInterval(interval);
  }, [user]);

  return timer;
}