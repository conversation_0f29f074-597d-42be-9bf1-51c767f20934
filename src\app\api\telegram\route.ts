// 6. src/app/api/telegram/route.ts (API để xử lý webhook từ Telegram Bot)
import { NextRequest, NextResponse } from "next/server";

const BOT_TOKEN = process.env.TELEGRAM_BOT_TOKEN;

export async function POST(request: NextRequest) {
    try {
        const body = await request.json();

        // Xử lý webhook từ Telegram Bot
        if (body.message) {
            const chatId = body.message.chat.id;
            const text = body.message.text;

            if (text === "/start") {
                // Gửi Mini App button
                await fetch(`https://api.telegram.org/bot${BOT_TOKEN}/sendMessage`, {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({
                        chat_id: chatId,
                        text: "Welcome! Tap the button below to launch the Mini App:",
                        reply_markup: {
                            inline_keyboard: [
                                [
                                    {
                                        text: "🚀 Open Mini App",
                                        web_app: { url: process.env.NEXT_PUBLIC_APP_URL }
                                    }
                                ]
                            ]
                        }
                    })
                });
            }
        }

        return NextResponse.json({ ok: true });
    } catch (error) {
        console.error("Telegram webhook error:", error);
        return NextResponse.json({ error: "Internal error" }, { status: 500 });
    }
}
