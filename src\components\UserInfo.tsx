// src/components/UserInfo.tsx
"use client";

import { useGameActions } from "@/hooks/useGameActions";
import { useUserSync } from "@/hooks/useUserSync";
import { calculateEnergyRegenTime, getExpToNextLevel } from "@/lib/supabase";
import Image from "next/image";
import { useEffect, useState } from "react";

interface UserInfoProps {
    showGameStats?: boolean;
    autoRefresh?: boolean;
    refreshInterval?: number; // milliseconds
}

export function UserInfo({
    showGameStats = true,
    autoRefresh = true,
    refreshInterval = 60000 // 1 phút
}: UserInfoProps) {
    const { user, loading, error, refreshUser } = useUserSync();
    const gameActions = useGameActions();
    const [energyRegenTime, setEnergyRegenTime] = useState<number>(0);

    // Tự động refresh user data để cập nhật energy
    useEffect(() => {
        if (!autoRefresh || !user) return;

        const interval = setInterval(() => {
            refreshUser();
        }, refreshInterval);

        return () => clearInterval(interval);
    }, [autoRefresh, refreshInterval, user, refreshUser]);

    // Tính thời gian hồi energy
    useEffect(() => {
        if (user && user.energy < user.energy_total) {
            const regenTime = calculateEnergyRegenTime(user.energy, user.energy_total);
            setEnergyRegenTime(regenTime);

            // Cập nhật countdown mỗi phút
            const interval = setInterval(() => {
                setEnergyRegenTime((prev) => Math.max(0, prev - 1));
            }, 60000);

            return () => clearInterval(interval);
        } else {
            setEnergyRegenTime(0);
        }
    }, [user]);

    // Format thời gian
    const formatTime = (minutes: number): string => {
        if (minutes <= 0) return "Full";
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
    };

    // Format số balance
    const formatBalance = (balance: number): string => {
        return new Intl.NumberFormat("vi-VN", {
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
        }).format(balance);
    };

    if (loading && !user) {
        return (
            <div className="p-4 bg-white rounded-lg shadow-sm border">
                <div className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-600 text-sm">Lỗi: {error}</p>
                <button
                    onClick={refreshUser}
                    className="mt-2 px-3 py-1 bg-red-100 text-red-700 rounded text-sm hover:bg-red-200">
                    Thử lại
                </button>
            </div>
        );
    }

    if (!user) {
        return (
            <div className="p-4 bg-gray-50 border rounded-lg">
                <p className="text-gray-600 text-sm">Không tìm thấy thông tin người dùng</p>
            </div>
        );
    }

    return (
        <div className="p-4 bg-white rounded-lg shadow-sm border">
            {/* Thông tin cơ bản */}
            <div className="flex items-center gap-3 mb-4">
                {user.photo_url && (
                    <Image
                        src={user.photo_url}
                        alt="Avatar"
                        width={48}
                        height={48}
                        className="w-12 h-12 rounded-full"
                    />
                )}
                <div>
                    <h3 className="font-semibold text-lg">
                        {user.first_name} {user.last_name}
                    </h3>
                    {user.username && <p className="text-gray-600 text-sm">@{user.username}</p>}
                    {user.premium && (
                        <span className="inline-block px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
                            Premium
                        </span>
                    )}
                </div>
            </div>

            {/* Game stats */}
            {showGameStats && (
                <div className="space-y-3">
                    {/* Balance */}
                    <div className="flex justify-between items-center">
                        <span className="text-gray-600">💰 HUFI Token:</span>
                        <span className="font-semibold text-yellow-600">{formatBalance(user.balance)}</span>
                    </div>

                    {/* Level */}
                    <div className="flex justify-between items-center">
                        <span className="text-gray-600">🏆 Level:</span>
                        <span className="font-semibold text-blue-600">{user.level}</span>
                    </div>

                    {/* Level Progress */}
                    <div>
                        <div className="flex justify-between items-center mb-1">
                            <span className="text-gray-600 text-sm">📊 EXP:</span>
                            <span className="text-sm text-gray-500">
                                {user.level_progress}/100
                                <span className="text-xs ml-1">(còn {getExpToNextLevel(user.level_progress)})</span>
                            </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${user.level_progress}%` }}
                            />
                        </div>
                    </div>

                    {/* Energy */}
                    <div>
                        <div className="flex justify-between items-center mb-1">
                            <span className="text-gray-600">⚡ Năng lượng:</span>
                            <span className="font-semibold">
                                {user.energy}/{user.energy_total}
                                {energyRegenTime > 0 && (
                                    <span className="text-xs text-gray-500 ml-2">
                                        (đầy trong {formatTime(energyRegenTime)})
                                    </span>
                                )}
                            </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                                className="bg-green-500 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${(user.energy / user.energy_total) * 100}%` }}
                            />
                        </div>
                    </div>

                    {/* Refresh button */}
                    <div className="pt-2 border-t">
                        <button
                            onClick={refreshUser}
                            disabled={loading}
                            className="w-full px-3 py-2 bg-blue-50 text-blue-600 rounded-lg text-sm hover:bg-blue-100 disabled:opacity-50 transition-colors">
                            {loading ? "Đang cập nhật..." : "🔄 Cập nhật thông tin"}
                        </button>
                    </div>
                </div>
            )}
        </div>
    );
}
