// src/core/i18n/client-locale.ts
"use client";

import { mapTelegramLanguageToLocale, defaultLocale } from "./config";

const LOCALE_STORAGE_KEY = "app_locale";
const TELEGRAM_LOCALE_KEY = "telegram_detected_locale";

/**
 * Client-side locale management
 */
export class ClientLocaleManager {
    private static instance: ClientLocaleManager;
    
    private constructor() {}
    
    static getInstance(): ClientLocaleManager {
        if (!ClientLocaleManager.instance) {
            ClientLocaleManager.instance = new ClientLocaleManager();
        }
        return ClientLocaleManager.instance;
    }

    /**
     * Set locale in localStorage (client-side only)
     */
    setLocale(locale: string): void {
        if (typeof window !== 'undefined') {
            localStorage.setItem(LOCALE_STORAGE_KEY, locale);
            console.log('Locale set to:', locale);
        }
    }

    /**
     * Get current locale from localStorage
     */
    getLocale(): string {
        if (typeof window !== 'undefined') {
            // First check manual setting
            const manualLocale = localStorage.getItem(LOCALE_STORAGE_KEY);
            if (manualLocale) {
                return manualLocale;
            }

            // Then check Telegram detected
            const telegramLocale = localStorage.getItem(TELEGRAM_LOCALE_KEY);
            if (telegramLocale) {
                return telegramLocale;
            }
        }
        
        return defaultLocale;
    }

    /**
     * Set locale based on Telegram language
     */
    setTelegramLocale(telegramLanguageCode?: string): string {
        const detectedLocale = mapTelegramLanguageToLocale(telegramLanguageCode);
        
        if (typeof window !== 'undefined') {
            // Only set if no manual locale exists
            const existingManualLocale = localStorage.getItem(LOCALE_STORAGE_KEY);
            if (!existingManualLocale) {
                localStorage.setItem(TELEGRAM_LOCALE_KEY, detectedLocale);
                console.log(`Auto-detected locale from Telegram: ${telegramLanguageCode} -> ${detectedLocale}`);
            }
        }
        
        return detectedLocale;
    }

    /**
     * Clear all locale settings
     */
    clearLocale(): void {
        if (typeof window !== 'undefined') {
            localStorage.removeItem(LOCALE_STORAGE_KEY);
            localStorage.removeItem(TELEGRAM_LOCALE_KEY);
        }
    }

    /**
     * Get locale source info
     */
    getLocaleSource(): {
        current: string;
        source: 'manual' | 'telegram' | 'default';
        telegramDetected?: string;
    } {
        if (typeof window !== 'undefined') {
            const manualLocale = localStorage.getItem(LOCALE_STORAGE_KEY);
            const telegramLocale = localStorage.getItem(TELEGRAM_LOCALE_KEY);
            
            if (manualLocale) {
                return {
                    current: manualLocale,
                    source: 'manual',
                    telegramDetected: telegramLocale || undefined
                };
            }
            
            if (telegramLocale) {
                return {
                    current: telegramLocale,
                    source: 'telegram'
                };
            }
        }
        
        return {
            current: defaultLocale,
            source: 'default'
        };
    }
}

// Export singleton instance
export const clientLocaleManager = ClientLocaleManager.getInstance();