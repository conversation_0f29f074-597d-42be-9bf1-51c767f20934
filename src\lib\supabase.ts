// src/lib/supabase.ts
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

if (!supabaseUrl) {
  throw new Error('Missing NEXT_PUBLIC_SUPABASE_URL');
}

if (!supabaseAnonKey) {
  throw new Error('Missing NEXT_PUBLIC_SUPABASE_ANON_KEY');
}

// Client cho frontend (sử dụng anon key)
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Function để tạo admin client (chỉ dùng trong server-side)
export function createSupabaseAdmin() {
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  
  if (!supabaseServiceKey) {
    throw new Error('Missing SUPABASE_SERVICE_ROLE_KEY - only available on server side');
  }

  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
}

// Type definitions cho user data từ Telegram
export interface TelegramUser {
  id: number;
  first_name: string;
  last_name?: string;
  username?: string;
  photo_url?: string;
  language_code?: string;
  auth_date?: number; // Optional cho development
  premium?: boolean;
}

// Type definitions cho user data trong database (cập nhật với các trường mới)
export interface DatabaseUser {
  id: number;
  first_name: string;
  last_name: string | null;
  username: string | null;
  photo_url: string | null;
  language_code: string | null;
  auth_date: string;
  premium: boolean;
  balance: number; // Token HUFI
  energy_total: number; // Tổng năng lượng có thể có
  energy: number; // Năng lượng hiện tại
  level: number; // Level người dùng
  level_progress: number; // Tiến trình level (0-100)
  last_energy_update: string; // Thời điểm cập nhật năng lượng cuối
  created_at: string;
  updated_at: string;
}

// Interface cho kết quả sử dụng năng lượng
export interface EnergyUsageResult {
  success: boolean;
  current_energy: number;
  message: string;
}

// Interface cho kết quả thêm EXP
export interface ExperienceResult {
  success: boolean;
  new_level: number;
  new_progress: number;
  level_up: boolean;
}

// Helper functions cho game mechanics

/**
 * Sử dụng năng lượng của user
 * @param userId ID của user
 * @param amount Số lượng năng lượng cần sử dụng (mặc định là 1)
 * @returns Kết quả sử dụng năng lượng
 */
export async function useEnergy(userId: number, amount: number = 1): Promise<EnergyUsageResult> {
  try {
    // Sử dụng client-side RPC call với anon key
    const { data, error } = await supabase
      .rpc('use_energy', { 
        user_id: userId, 
        amount: amount 
      });

    if (error) {
      console.error('Error using energy:', error);
      return {
        success: false,
        current_energy: 0,
        message: 'Database error'
      };
    }

    return data[0] as EnergyUsageResult;
  } catch (error) {
    console.error('Unexpected error using energy:', error);
    return {
      success: false,
      current_energy: 0,
      message: 'Unexpected error'
    };
  }
}

/**
 * Thêm kinh nghiệm cho user và xử lý level up
 * @param userId ID của user
 * @param expAmount Số lượng EXP cần thêm
 * @returns Kết quả thêm EXP
 */
export async function addExperience(userId: number, expAmount: number): Promise<ExperienceResult> {
  try {
    const { data, error } = await supabase
      .rpc('add_experience', { 
        user_id: userId, 
        exp_amount: expAmount 
      });

    if (error) {
      console.error('Error adding experience:', error);
      return {
        success: false,
        new_level: 0,
        new_progress: 0,
        level_up: false
      };
    }

    return data[0] as ExperienceResult;
  } catch (error) {
    console.error('Unexpected error adding experience:', error);
    return {
      success: false,
      new_level: 0,
      new_progress: 0,
      level_up: false
    };
  }
}

/**
 * Lấy thông tin user với năng lượng được cập nhật tự động
 * @param userId ID của user
 * @returns Thông tin user đầy đủ
 */
export async function getUserWithUpdatedEnergy(userId: number): Promise<DatabaseUser | null> {
  try {
    const { data, error } = await supabase
      .rpc('get_user_with_updated_energy', { 
        user_id: userId 
      });

    if (error) {
      console.error('Error getting user with updated energy:', error);
      return null;
    }

    return data[0] as DatabaseUser || null;
  } catch (error) {
    console.error('Unexpected error getting user:', error);
    return null;
  }
}

/**
 * Cập nhật balance (token HUFI) của user
 * @param userId ID của user
 * @param amount Số lượng token cần thêm/trừ (có thể âm để trừ)
 * @returns Success status
 */
export async function updateBalance(userId: number, amount: number): Promise<boolean> {
  try {
    // Sử dụng cách cập nhật an toàn hơn với client
    const { data: currentUser } = await supabase
      .from('users')
      .select('balance')
      .eq('id', userId)
      .single();

    if (!currentUser) {
      console.error('User not found');
      return false;
    }

    const newBalance = Number(currentUser.balance) + amount;
    
    // Đảm bảo balance không âm
    if (newBalance < 0) {
      console.error('Insufficient balance');
      return false;
    }

    const { error } = await supabase
      .from('users')
      .update({ 
        balance: newBalance,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (error) {
      console.error('Error updating balance:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Unexpected error updating balance:', error);
    return false;
  }
}

/**
 * Cập nhật năng lượng tối đa của user
 * @param userId ID của user
 * @param newEnergyTotal Năng lượng tối đa mới
 * @returns Success status
 */
export async function updateEnergyTotal(userId: number, newEnergyTotal: number): Promise<boolean> {
  try {
    const { data: currentUser } = await supabase
      .from('users')
      .select('energy, energy_total')
      .eq('id', userId)
      .single();

    if (!currentUser) {
      console.error('User not found');
      return false;
    }

    // Cập nhật energy hiện tại nếu nó lớn hơn energy_total mới
    const newEnergy = Math.min(currentUser.energy, newEnergyTotal);

    const { error } = await supabase
      .from('users')
      .update({ 
        energy_total: newEnergyTotal,
        energy: newEnergy,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (error) {
      console.error('Error updating energy total:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Unexpected error updating energy total:', error);
    return false;
  }
}

/**
 * Tính thời gian cần thiết để hồi đầy năng lượng
 * @param currentEnergy Năng lượng hiện tại
 * @param maxEnergy Năng lượng tối đa
 * @returns Thời gian tính bằng phút
 */
export function calculateEnergyRegenTime(currentEnergy: number, maxEnergy: number): number {
  if (currentEnergy >= maxEnergy) return 0;
  return (maxEnergy - currentEnergy) * 20; // 20 phút mỗi năng lượng
}

/**
 * Tính EXP cần thiết để lên level tiếp theo
 * @param currentProgress Tiến trình hiện tại (0-100)
 * @returns EXP cần thiết
 */
export function getExpToNextLevel(currentProgress: number): number {
  return 100 - currentProgress;
}