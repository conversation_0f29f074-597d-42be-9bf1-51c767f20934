"use client";

import { Select } from "@telegram-apps/telegram-ui";
import { useLocale } from "next-intl";
import { FC } from "react";

import { localesMap } from "@/core/i18n/config";
import { setLocale } from "@/core/i18n/locale";
import { Locale } from "@/core/i18n/types";

export const LocaleSwitcher: FC = () => {
    const locale = useLocale();

    const onChange = (value: string) => {
        const selectedLocale = value as Locale;
        setLocale(selectedLocale);
    };

    return (
        <Select value={locale} onChange={({ target }) => onChange(target.value)}>
            {localesMap.map((localeOption) => (
                <option key={localeOption.key} value={localeOption.key}>
                    {localeOption.title}
                </option>
            ))}
        </Select>
    );
};
