import { useEffect, useState } from "react";

export type NavigationTab = "tasks" | "leaderboard" | "hunt" | "refer" | "profile";

// Define available routes
const routes = {
    "": "hunt",
    main: "hunt",
    tasks: "tasks",
    leaderboard: "leaderboard",
    hunt: "hunt",
    refer: "refer",
    profile: "profile"
} as const;

function hashToTab(hash: string): NavigationTab {
    switch (hash) {
        case "tasks":
            return "tasks";
        case "leaderboard":
            return "leaderboard";
        case "hunt":
        case "main":
        case "":
            return "hunt";
        case "refer":
            return "refer";
        case "profile":
            return "profile";
        default:
            return "hunt"; // default tab
    }
}

export function useHashRouting() {
    const [activeTab, setActiveTab] = useState<NavigationTab>("hunt");
    const [currentHash, setCurrentHash] = useState("");

    useEffect(() => {
        if (typeof window === "undefined") return;

        const handleHashChange = () => {
            let hash = window.location.hash.slice(1);
            console.log("useHashRouting - Raw hash:", hash);

            if (hash.startsWith("tgWebAppData")) {
                hash = "";
            }

            if (hash === "" || Object.keys(routes).includes(hash)) {
                console.log("useHashRouting - Valid hash, setting currentHash to:", hash);
                setCurrentHash(hash);
                const newTab = hashToTab(hash);
                console.log("useHashRouting - Setting activeTab to:", newTab);
                setActiveTab(newTab);
            } else {
                const mainHash = hash?.split("#")[1] ?? localStorage.getItem("screenIndex") ?? "0";
                console.log("useHashRouting - Complex hash detected, mainHash:", mainHash);
                window.history.replaceState(null, "", `#main#${mainHash}`);
                setCurrentHash("main");
                setActiveTab("hunt");
            }
        };

        window.addEventListener("hashchange", handleHashChange);
        handleHashChange();

        return () => window.removeEventListener("hashchange", handleHashChange);
    }, []);

    const isHashActive = (itemHash: string): boolean => {
        return (
            currentHash === itemHash ||
            (currentHash === "" && itemHash === "hunt") ||
            (currentHash === "main" && itemHash === "hunt")
        );
    };

    const handleNavigationClick = (itemHash: string) => {
        console.log("useHashRouting - Navigation clicked:", itemHash);
        if (itemHash !== "hunt") {
            localStorage.setItem("screenIndex", itemHash);
        }
    };

    return {
        activeTab,
        currentHash,
        isHashActive,
        handleNavigationClick,
        routes
    };
}
