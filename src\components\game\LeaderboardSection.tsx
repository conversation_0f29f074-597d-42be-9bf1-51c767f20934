"use client";

import { useUserSync } from "@/hooks/useUserSync";
import { useGameStore } from "@/store/gameStore";

const LeaderboardSection = () => {
    const { user } = useUserSync();
    const { tokens } = useGameStore();

    // Use database balance if available, fallback to game store
    const userTokens = user ? user.balance : tokens;

    const leaderboardData = [
        { rank: 1, name: "Top Hunter", tokens: 5240, badge: "👑" },
        { rank: 2, name: "Treasure Master", tokens: 4890, badge: "🥈" },
        { rank: 3, name: "Gold Seeker", tokens: 4320, badge: "🥉" },
        { rank: 4, name: "Lucky Explorer", tokens: 3875, badge: "" },
        { rank: 5, name: "Adventure Pro", tokens: 3456, badge: "" },
        {
            rank: 6,
            name: user ? `${user.first_name} ${user.last_name || ""}`.trim() : "You",
            tokens: userTokens,
            badge: "",
            isUser: true
        }
    ];

    return (
        <div className="w-full h-full p-6 overflow-y-auto">
            <h2 className="text-2xl font-bold mb-6 text-gray-800">Leaderboard</h2>

            <div className="space-y-3">
                {leaderboardData.map((player) => (
                    <div
                        key={player.rank}
                        className={`bg-white rounded-xl p-4 shadow-md border ${
                            player.isUser ? "border-purple-300 bg-purple-50" : "border-gray-100"
                        }`}>
                        <div className="flex items-center">
                            <div
                                className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-bold mr-4 ${
                                    player.rank === 1
                                        ? "bg-yellow-500"
                                        : player.rank === 2
                                          ? "bg-gray-400"
                                          : player.rank === 3
                                            ? "bg-orange-400"
                                            : "bg-gray-300"
                                }`}>
                                {player.rank}
                            </div>

                            <div className="flex-1">
                                <div className="flex items-center">
                                    <h3 className={`font-bold ${player.isUser ? "text-purple-700" : "text-gray-800"}`}>
                                        {player.name}
                                    </h3>
                                    {player.badge && <span className="ml-2 text-lg">{player.badge}</span>}
                                    {player.isUser && (
                                        <span className="ml-2 text-xs bg-purple-600 text-white px-2 py-1 rounded-full">
                                            YOU
                                        </span>
                                    )}
                                </div>
                                <p className="text-sm text-gray-600">{player.tokens.toLocaleString()} tokens</p>
                            </div>

                            {player.rank <= 3 && (
                                <div className="text-2xl">
                                    {player.rank === 1 ? "👑" : player.rank === 2 ? "🥈" : "🥉"}
                                </div>
                            )}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default LeaderboardSection;
